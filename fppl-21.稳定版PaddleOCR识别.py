import win32com.client
from datetime import datetime
import os
import tkinter as tk
from tkinter import filedialog, ttk
from tkinter import messagebox
import re
import cv2
import numpy as np
from paddleocr import PaddleOCR
import fitz  # PyMuPDF
import gc
import traceback

# 全局OCR对象，延迟初始化
ocr = None

def get_ocr_instance():
    """获取OCR实例，延迟初始化"""
    global ocr
    if ocr is None:
        print("正在初始化PaddleOCR...")
        try:
            ocr = PaddleOCR(lang='ch')
            print("PaddleOCR初始化完成！")
        except Exception as e:
            print(f"PaddleOCR初始化失败: {str(e)}")
            raise
    return ocr

def extract_invoice_info_paddleocr(ocr_result):
    """从PaddleOCR结果中提取发票信息"""
    result = {
        'InvoiceTypeOrg': '',
        'AmountInFiguers': '',
        'TotalAmount': '',
        'CommodityTaxRate': '',
        'TotalTax': '',
        'SellerName': '',
        'Remarks': ''
    }

    if not ocr_result or len(ocr_result) == 0:
        return result
    
    try:
        # 获取第一页的识别结果
        page_result = ocr_result[0]
        texts = page_result.get('rec_texts', [])
        scores = page_result.get('rec_scores', [])
        polys = page_result.get('rec_polys', [])
        
        # 将所有识别的文本保存，按y坐标排序（从上到下）
        all_text = []
        for i, (text, score) in enumerate(zip(texts, scores)):
            if i < len(polys):
                bbox = polys[i]
                # 计算文本框的中心y坐标用于排序
                center_y = (bbox[0][1] + bbox[2][1]) / 2
                all_text.append((text.strip(), score, center_y, bbox))
        
        # 按y坐标排序，确保按从上到下的顺序处理
        all_text.sort(key=lambda x: x[2])

        print(f"开始提取发票信息...")
        print(f"识别到 {len(all_text)} 个文本区域")

        # 遍历所有文本进行信息提取
        for i, (line, confidence, center_y, bbox) in enumerate(all_text):
            line = str(line).strip()
            print(f"处理文本: {line} (置信度: {confidence:.3f})")

            # 发票类型识别
            if not result['InvoiceTypeOrg']:
                if '增值税专用发票' in line:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                    print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
                elif '增值税普通发票' in line:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                    print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")

            # 价税合计识别
            if not result['TotalAmount']:
                if any(keyword in line for keyword in ['价税合计', '合计金额', '总计', '￥', '¥']):
                    amount_patterns = [
                        r'￥(\d+\.?\d*)',
                        r'¥(\d+\.?\d*)', 
                        r'(\d+\.\d{2})',
                        r'合计.*?(\d+\.\d{2})',
                        r'总.*?(\d+\.\d{2})'
                    ]
                    for pattern in amount_patterns:
                        match = re.search(pattern, line)
                        if match:
                            amount_str = match.group(1)
                            try:
                                amount_val = float(amount_str)
                                if amount_val > 0:
                                    result['TotalAmount'] = amount_str
                                    result['AmountInFiguers'] = amount_str
                                    print(f"识别价税合计: {line} -> {amount_str}")
                                    break
                            except:
                                continue

            # 税额识别
            if not result['TotalTax']:
                if '税额' in line or ('税' in line and re.search(r'\d+\.\d{2}', line)):
                    tax_patterns = [
                        r'税额.*?(\d+\.\d{2})',
                        r'税.*?(\d+\.\d{2})',
                        r'(\d+\.\d{2})'
                    ]
                    for pattern in tax_patterns:
                        match = re.search(pattern, line)
                        if match:
                            tax_str = match.group(1)
                            try:
                                tax_amount = float(tax_str)
                                if result['TotalAmount']:
                                    total_amount = float(result['TotalAmount'])
                                    if 0 < tax_amount < total_amount:
                                        result['TotalTax'] = tax_str
                                        print(f"识别税额: {line} -> {tax_str}")
                                        break
                                elif 0 < tax_amount < 100000:
                                    result['TotalTax'] = tax_str
                                    print(f"识别税额: {line} -> {tax_str}")
                                    break
                            except:
                                continue

            # 税率识别
            if not result['CommodityTaxRate']:
                if '税率' in line or re.search(r'\d+%', line):
                    rate_patterns = [
                        r'(\d+(?:\.\d+)?)%',
                        r'税率.*?(\d+(?:\.\d+)?)%'
                    ]
                    for pattern in rate_patterns:
                        match = re.search(pattern, line)
                        if match:
                            rate_str = match.group(1)
                            try:
                                rate_val = float(rate_str)
                                if 0 <= rate_val <= 20:
                                    result['CommodityTaxRate'] = [rate_str + '%']
                                    print(f"识别税率: {line} -> {rate_str}%")
                                    break
                            except:
                                continue

            # 销售方名称识别
            if not result['SellerName']:
                if '销售方' in line and '名称' in line:
                    seller_patterns = [
                        r'名称[:：]\s*(.+)',
                        r'销售方.*?名称.*?[:：]\s*(.+)'
                    ]
                    for pattern in seller_patterns:
                        match = re.search(pattern, line)
                        if match:
                            seller_name = match.group(1).strip()
                            if len(seller_name) > 3:
                                result['SellerName'] = seller_name
                                print(f"识别销售方: {line} -> {seller_name}")
                                break
                    
                    # 如果当前行没找到，查找下一行
                    if not result['SellerName'] and i + 1 < len(all_text):
                        next_line = all_text[i + 1][0]
                        if '公司' in next_line or '有限' in next_line:
                            result['SellerName'] = next_line.strip()
                            print(f"识别销售方(下一行): {next_line}")

            # 如果还没找到销售方，查找包含公司名称的行
            if not result['SellerName'] and ('公司' in line or '有限' in line) and len(line) > 5:
                if '购' not in line and '买方' not in line:
                    if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设', '电力']):
                        result['SellerName'] = line.strip()
                        print(f"识别销售方(公司名): {line}")

            # 备注信息（开户行和账号）
            if '开户行' in line or '账号' in line or '银行' in line:
                result['Remarks'] = result['Remarks'] + line + ';'

        print(f"信息提取完成: {result}")
        return result
        
    except Exception as e:
        print(f"提取发票信息时出错: {str(e)}")
        traceback.print_exc()
        return result

def pdf_to_image_pymupdf(pdf_path):
    """使用PyMuPDF将PDF转换为图片"""
    try:
        doc = fitz.open(pdf_path)
        page = doc[0]

        # 设置适中的缩放比例
        mat = fitz.Matrix(2.0, 2.0)
        pix = page.get_pixmap(matrix=mat)

        temp_image_path = "temp_invoice_stable.png"
        pix.save(temp_image_path)
        doc.close()

        print(f"PyMuPDF转换完成: {temp_image_path}")
        return temp_image_path
    except Exception as e:
        print(f"PyMuPDF转换失败: {str(e)}")
        return None

def recognize_invoice_paddleocr(file_path):
    """使用PaddleOCR识别发票信息"""
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return None

    print(f"开始处理发票: {file_path}")

    temp_image_path = None
    try:
        # 获取OCR实例
        ocr_instance = get_ocr_instance()
        
        # 如果是PDF，转换为图片
        if file_path.lower().endswith('.pdf'):
            print("正在转换PDF为图片...")
            temp_image_path = pdf_to_image_pymupdf(file_path)
            
            if not temp_image_path:
                print("PDF转换失败")
                return None

            file_path = temp_image_path
            print("PDF转换完成")

        # 使用PaddleOCR识别图片
        print("正在进行OCR识别...")
        print(f"使用图片文件: {file_path}")
        
        try:
            result = ocr_instance.predict(file_path)
            print("OCR识别完成")
        except Exception as ocr_error:
            print(f"OCR识别过程中出错: {str(ocr_error)}")
            traceback.print_exc()
            return None

        if not result or len(result) == 0:
            print("OCR识别结果为空")
            return None

        # 获取识别的文本数量
        page_result = result[0]
        text_count = len(page_result.get('rec_texts', []))
        print(f"OCR识别到 {text_count} 个文本区域")

        # 从OCR结果中提取发票信息
        invoice_data = extract_invoice_info_paddleocr(result)
        print(f"识别结果: {invoice_data}")
        return invoice_data

    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        traceback.print_exc()
        return None
    finally:
        # 清理临时文件
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
                print("临时文件已清理")
            except:
                pass
        
        # 强制垃圾回收
        gc.collect()

# 简化的测试函数
def test_invoice_recognition():
    """测试发票识别功能"""
    test_file = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf"
    
    if os.path.exists(test_file):
        print("开始测试发票识别...")
        result = recognize_invoice_paddleocr(test_file)
        
        if result:
            print("\n=== 测试结果 ===")
            for key, value in result.items():
                print(f"{key}: {value}")
            print("\n✓ 测试成功！")
        else:
            print("\n✗ 测试失败")
    else:
        print("测试文件不存在")

if __name__ == "__main__":
    test_invoice_recognition()
