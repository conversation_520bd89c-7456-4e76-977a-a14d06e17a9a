info = {
    "name": "it",
    "date_order": "DMY",
    "january": [
        "gen",
        "gennaio"
    ],
    "february": [
        "feb",
        "febbraio"
    ],
    "march": [
        "mar",
        "marzo"
    ],
    "april": [
        "apr",
        "aprile"
    ],
    "may": [
        "mag",
        "maggio"
    ],
    "june": [
        "giu",
        "giugno"
    ],
    "july": [
        "lug",
        "luglio"
    ],
    "august": [
        "ago",
        "agosto"
    ],
    "september": [
        "set",
        "settembre"
    ],
    "october": [
        "ott",
        "ottobre"
    ],
    "november": [
        "nov",
        "novembre"
    ],
    "december": [
        "dic",
        "dicembre"
    ],
    "monday": [
        "lun",
        "lunedì"
    ],
    "tuesday": [
        "mar",
        "marted<PERSON>"
    ],
    "wednesday": [
        "mer",
        "mercoled<PERSON>"
    ],
    "thursday": [
        "gio",
        "gioved<PERSON>"
    ],
    "friday": [
        "ven",
        "venerd<PERSON>"
    ],
    "saturday": [
        "sab",
        "sabato"
    ],
    "sunday": [
        "dom",
        "domenica"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "anno",
        "anni"
    ],
    "month": [
        "mese",
        "mesi"
    ],
    "week": [
        "sett",
        "settimana",
        "settimane"
    ],
    "day": [
        "g",
        "giorno",
        "giorni"
    ],
    "hour": [
        "h",
        "ora",
        "ore"
    ],
    "minute": [
        "m",
        "min",
        "minuto",
        "minuti"
    ],
    "second": [
        "s",
        "sec",
        "secondo",
        "secondi"
    ],
    "relative-type": {
        "0 day ago": [
            "oggi"
        ],
        "0 hour ago": [
            "quest'ora"
        ],
        "0 minute ago": [
            "questo minuto"
        ],
        "0 month ago": [
            "questo mese"
        ],
        "0 second ago": [
            "ora"
        ],
        "0 week ago": [
            "questa settimana"
        ],
        "0 year ago": [
            "quest'anno"
        ],
        "1 day ago": [
            "ieri"
        ],
        "1 month ago": [
            "mese scorso"
        ],
        "1 week ago": [
            "settimana scorsa"
        ],
        "1 year ago": [
            "anno scorso"
        ],
        "in 1 day": [
            "domani"
        ],
        "in 1 month": [
            "mese prossimo"
        ],
        "in 1 week": [
            "settimana prossima"
        ],
        "in 1 year": [
            "anno prossimo"
        ],
        "2 day ago": [
            "altro ieri"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) g fa",
            "(\\d+[.,]?\\d*) gg fa",
            "(\\d+[.,]?\\d*) giorni fa",
            "(\\d+[.,]?\\d*) giorno fa"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) h fa",
            "(\\d+[.,]?\\d*) ora fa",
            "(\\d+[.,]?\\d*) ore fa"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) min fa",
            "(\\d+[.,]?\\d*) minuti fa",
            "(\\d+[.,]?\\d*) minuto fa"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) mese fa",
            "(\\d+[.,]?\\d*) mesi fa"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) s fa",
            "(\\d+[.,]?\\d*) sec fa",
            "(\\d+[.,]?\\d*) secondi fa",
            "(\\d+[.,]?\\d*) secondo fa"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) sett fa",
            "(\\d+[.,]?\\d*) settimana fa",
            "(\\d+[.,]?\\d*) settimane fa"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) anni fa",
            "(\\d+[.,]?\\d*) anno fa"
        ],
        "in \\1 day": [
            "tra (\\d+[.,]?\\d*) g",
            "tra (\\d+[.,]?\\d*) gg",
            "tra (\\d+[.,]?\\d*) giorni",
            "tra (\\d+[.,]?\\d*) giorno"
        ],
        "in \\1 hour": [
            "tra (\\d+[.,]?\\d*) h",
            "tra (\\d+[.,]?\\d*) ora",
            "tra (\\d+[.,]?\\d*) ore"
        ],
        "in \\1 minute": [
            "tra (\\d+[.,]?\\d*) min",
            "tra (\\d+[.,]?\\d*) minuti",
            "tra (\\d+[.,]?\\d*) minuto"
        ],
        "in \\1 month": [
            "tra (\\d+[.,]?\\d*) mese",
            "tra (\\d+[.,]?\\d*) mesi"
        ],
        "in \\1 second": [
            "tra (\\d+[.,]?\\d*) s",
            "tra (\\d+[.,]?\\d*) sec",
            "tra (\\d+[.,]?\\d*) secondi",
            "tra (\\d+[.,]?\\d*) secondo"
        ],
        "in \\1 week": [
            "tra (\\d+[.,]?\\d*) sett",
            "tra (\\d+[.,]?\\d*) settimana",
            "tra (\\d+[.,]?\\d*) settimane"
        ],
        "in \\1 year": [
            "tra (\\d+[.,]?\\d*) anni",
            "tra (\\d+[.,]?\\d*) anno"
        ]
    },
    "locale_specific": {
        "it-CH": {
            "name": "it-CH"
        },
        "it-SM": {
            "name": "it-SM"
        },
        "it-VA": {
            "name": "it-VA"
        }
    },
    "skip": [
        "circa",
        "e",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "fa"
    ],
    "in": [
        "in"
    ],
    "simplifications": [
        {
            "(\\d+[.,]?\\d*)\\s+ora": "\\1 ore"
        }
    ]
}
