#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试打包后的exe文件
"""

import os
import subprocess
import time

def test_exe():
    """测试exe文件是否正常工作"""
    exe_path = r"D:\vscode project\fpcl\dist\发票处理系统.exe"
    
    print("🧪 测试打包后的exe文件")
    print("=" * 50)
    
    # 检查文件是否存在
    if not os.path.exists(exe_path):
        print(f"❌ 错误: 找不到exe文件 {exe_path}")
        return False
    
    # 获取文件信息
    file_size = os.path.getsize(exe_path)
    size_mb = file_size / (1024 * 1024)
    print(f"✅ 找到exe文件: {exe_path}")
    print(f"📁 文件大小: {size_mb:.1f} MB")
    
    # 尝试启动程序（非阻塞）
    print("\n🚀 启动程序测试...")
    try:
        # 启动程序但不等待
        process = subprocess.Popen([exe_path], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 creationflags=subprocess.CREATE_NEW_CONSOLE)
        
        print("✅ 程序启动成功!")
        print(f"📊 进程ID: {process.pid}")
        
        # 等待几秒钟看程序是否稳定运行
        print("⏳ 等待5秒检查程序稳定性...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        poll_result = process.poll()
        if poll_result is None:
            print("✅ 程序运行稳定!")
            print("💡 您可以在弹出的窗口中测试程序功能")
            print("💡 测试完成后请手动关闭程序窗口")
            
            # 询问是否终止测试进程
            try:
                input("\n按Enter键终止测试进程...")
                process.terminate()
                print("✅ 测试进程已终止")
            except KeyboardInterrupt:
                process.terminate()
                print("\n✅ 测试进程已终止")
                
            return True
        else:
            print(f"❌ 程序异常退出，退出码: {poll_result}")
            # 读取错误信息
            stdout, stderr = process.communicate()
            if stderr:
                print(f"错误信息: {stderr.decode('utf-8', errors='ignore')}")
            return False
            
    except Exception as e:
        print(f"❌ 启动程序失败: {e}")
        return False

def main():
    """主函数"""
    success = test_exe()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 exe文件测试通过!")
        print("📋 测试结果:")
        print("  ✅ 文件存在且大小正常")
        print("  ✅ 程序可以正常启动")
        print("  ✅ 程序运行稳定")
        print("\n💡 您的exe文件已准备就绪，可以分发使用!")
        
        print("\n📦 分发建议:")
        print("1. 将exe文件复制到目标计算机")
        print("2. 确保目标计算机是Windows系统")
        print("3. 首次运行时可能需要管理员权限")
        print("4. 如果杀毒软件报警，请添加信任")
        
    else:
        print("❌ exe文件测试失败!")
        print("📋 可能的问题:")
        print("  - 打包过程中出现错误")
        print("  - 缺少必要的依赖文件")
        print("  - 系统兼容性问题")
        print("\n🔧 建议:")
        print("1. 重新运行打包脚本")
        print("2. 检查是否有杀毒软件干扰")
        print("3. 尝试在其他计算机上测试")

if __name__ == "__main__":
    main()
