info = {
    "name": "es",
    "date_order": "DMY",
    "january": [
        "ene",
        "enero"
    ],
    "february": [
        "feb",
        "febrero"
    ],
    "march": [
        "mar",
        "marzo"
    ],
    "april": [
        "abr",
        "abril"
    ],
    "may": [
        "may",
        "mayo"
    ],
    "june": [
        "jun",
        "junio"
    ],
    "july": [
        "jul",
        "julio"
    ],
    "august": [
        "ago",
        "agosto"
    ],
    "september": [
        "sept",
        "septiembre",
        "Setiembre",
        "Sep",
        "Set"
    ],
    "october": [
        "oct",
        "octubre"
    ],
    "november": [
        "nov",
        "noviembre"
    ],
    "december": [
        "dic",
        "diciembre"
    ],
    "monday": [
        "lun",
        "lunes",
        "Lu"
    ],
    "tuesday": [
        "mar",
        "martes"
    ],
    "wednesday": [
        "mié",
        "miércoles",
        "Mi"
    ],
    "thursday": [
        "jue",
        "jueves",
        "Ju"
    ],
    "friday": [
        "vie",
        "viernes",
        "Vi"
    ],
    "saturday": [
        "sáb",
        "sábado",
        "Sa"
    ],
    "sunday": [
        "dom",
        "domingo",
        "Do"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "a",
        "año",
        "años"
    ],
    "month": [
        "m",
        "mes",
        "meses"
    ],
    "week": [
        "sem",
        "semana",
        "semanas"
    ],
    "day": [
        "d",
        "día",
        "días"
    ],
    "hour": [
        "h",
        "hora",
        "horas"
    ],
    "minute": [
        "min",
        "minuto",
        "minutos"
    ],
    "second": [
        "s",
        "segundo",
        "segundos"
    ],
    "relative-type": {
        "0 day ago": [
            "hoy"
        ],
        "0 hour ago": [
            "esta hora"
        ],
        "0 minute ago": [
            "este minuto"
        ],
        "0 month ago": [
            "este mes"
        ],
        "0 second ago": [
            "ahora"
        ],
        "0 week ago": [
            "esta semana"
        ],
        "0 year ago": [
            "este año"
        ],
        "1 day ago": [
            "ayer"
        ],
        "1 month ago": [
            "el mes pasado"
        ],
        "1 week ago": [
            "la semana pasada"
        ],
        "1 year ago": [
            "el año pasado"
        ],
        "in 1 day": [
            "mañana"
        ],
        "in 1 month": [
            "el próximo mes"
        ],
        "in 1 week": [
            "la próxima semana"
        ],
        "in 1 year": [
            "el próximo año"
        ],
        "2 day ago": [
            "anteayer"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "hace (\\d+[.,]?\\d*) día",
            "hace (\\d+[.,]?\\d*) días"
        ],
        "\\1 hour ago": [
            "hace (\\d+[.,]?\\d*) h",
            "hace (\\d+[.,]?\\d*) hora",
            "hace (\\d+[.,]?\\d*) horas"
        ],
        "\\1 minute ago": [
            "hace (\\d+[.,]?\\d*) min",
            "hace (\\d+[.,]?\\d*) minuto",
            "hace (\\d+[.,]?\\d*) minutos"
        ],
        "\\1 month ago": [
            "hace (\\d+[.,]?\\d*) m",
            "hace (\\d+[.,]?\\d*) mes",
            "hace (\\d+[.,]?\\d*) meses"
        ],
        "\\1 second ago": [
            "hace (\\d+[.,]?\\d*) s",
            "hace (\\d+[.,]?\\d*) segundo",
            "hace (\\d+[.,]?\\d*) segundos"
        ],
        "\\1 week ago": [
            "hace (\\d+[.,]?\\d*) sem",
            "hace (\\d+[.,]?\\d*) semana",
            "hace (\\d+[.,]?\\d*) semanas"
        ],
        "\\1 year ago": [
            "hace (\\d+[.,]?\\d*) a",
            "hace (\\d+[.,]?\\d*) año",
            "hace (\\d+[.,]?\\d*) años"
        ],
        "in \\1 day": [
            "dentro de (\\d+[.,]?\\d*) día",
            "dentro de (\\d+[.,]?\\d*) días"
        ],
        "in \\1 hour": [
            "dentro de (\\d+[.,]?\\d*) h",
            "dentro de (\\d+[.,]?\\d*) hora",
            "dentro de (\\d+[.,]?\\d*) horas"
        ],
        "in \\1 minute": [
            "dentro de (\\d+[.,]?\\d*) min",
            "dentro de (\\d+[.,]?\\d*) minuto",
            "dentro de (\\d+[.,]?\\d*) minutos"
        ],
        "in \\1 month": [
            "dentro de (\\d+[.,]?\\d*) m",
            "dentro de (\\d+[.,]?\\d*) mes",
            "dentro de (\\d+[.,]?\\d*) meses"
        ],
        "in \\1 second": [
            "dentro de (\\d+[.,]?\\d*) s",
            "dentro de (\\d+[.,]?\\d*) segundo",
            "dentro de (\\d+[.,]?\\d*) segundos"
        ],
        "in \\1 week": [
            "dentro de (\\d+[.,]?\\d*) sem",
            "dentro de (\\d+[.,]?\\d*) semana",
            "dentro de (\\d+[.,]?\\d*) semanas"
        ],
        "in \\1 year": [
            "dentro de (\\d+[.,]?\\d*) a",
            "dentro de (\\d+[.,]?\\d*) año",
            "dentro de (\\d+[.,]?\\d*) años"
        ]
    },
    "locale_specific": {
        "es-419": {
            "name": "es-419",
            "september": [
                "sep"
            ]
        },
        "es-AR": {
            "name": "es-AR",
            "september": [
                "sep"
            ],
            "second": [
                "seg"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "hace (\\d+[.,]?\\d*) seg"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) seg"
                ]
            }
        },
        "es-BO": {
            "name": "es-BO",
            "september": [
                "sep"
            ]
        },
        "es-BR": {
            "name": "es-BR",
            "september": [
                "sep"
            ]
        },
        "es-BZ": {
            "name": "es-BZ",
            "september": [
                "sep"
            ]
        },
        "es-CL": {
            "name": "es-CL",
            "september": [
                "sep"
            ]
        },
        "es-CO": {
            "name": "es-CO",
            "september": [
                "sep"
            ]
        },
        "es-CR": {
            "name": "es-CR",
            "september": [
                "sep"
            ]
        },
        "es-CU": {
            "name": "es-CU",
            "september": [
                "sep"
            ]
        },
        "es-DO": {
            "name": "es-DO",
            "september": [
                "sep"
            ]
        },
        "es-EA": {
            "name": "es-EA"
        },
        "es-EC": {
            "name": "es-EC",
            "september": [
                "sep"
            ]
        },
        "es-GQ": {
            "name": "es-GQ"
        },
        "es-GT": {
            "name": "es-GT",
            "september": [
                "sep"
            ]
        },
        "es-HN": {
            "name": "es-HN",
            "september": [
                "sep"
            ]
        },
        "es-IC": {
            "name": "es-IC"
        },
        "es-MX": {
            "name": "es-MX",
            "september": [
                "sep"
            ],
            "relative-type": {
                "in 1 month": [
                    "el mes próximo"
                ],
                "in 1 week": [
                    "la semana próxima"
                ],
                "in 1 year": [
                    "el año próximo"
                ]
            },
            "relative-type-regex": {
                "in \\1 day": [
                    "en (\\d+[.,]?\\d*) día",
                    "en (\\d+[.,]?\\d*) días"
                ],
                "in \\1 hour": [
                    "en (\\d+[.,]?\\d*) h",
                    "en (\\d+[.,]?\\d*) n"
                ],
                "in \\1 minute": [
                    "en (\\d+[.,]?\\d*) min"
                ],
                "in \\1 month": [
                    "en (\\d+[.,]?\\d*) m",
                    "en (\\d+[.,]?\\d*) mes",
                    "en (\\d+[.,]?\\d*) meses"
                ],
                "in \\1 second": [
                    "en (\\d+[.,]?\\d*) s"
                ],
                "in \\1 week": [
                    "en (\\d+[.,]?\\d*) sem"
                ],
                "in \\1 year": [
                    "en (\\d+[.,]?\\d*) a"
                ]
            }
        },
        "es-NI": {
            "name": "es-NI",
            "september": [
                "sep"
            ]
        },
        "es-PA": {
            "name": "es-PA",
            "date_order": "MDY",
            "september": [
                "sep"
            ]
        },
        "es-PE": {
            "name": "es-PE",
            "september": [
                "set",
                "setiembre"
            ]
        },
        "es-PH": {
            "name": "es-PH"
        },
        "es-PR": {
            "name": "es-PR",
            "date_order": "MDY",
            "september": [
                "sep"
            ]
        },
        "es-PY": {
            "name": "es-PY",
            "second": [
                "seg"
            ],
            "relative-type-regex": {
                "\\1 second ago": [
                    "hace (\\d+[.,]?\\d*) seg"
                ],
                "in \\1 second": [
                    "dentro de (\\d+[.,]?\\d*) seg"
                ]
            }
        },
        "es-SV": {
            "name": "es-SV",
            "september": [
                "sep"
            ]
        },
        "es-US": {
            "name": "es-US",
            "september": [
                "sep"
            ]
        },
        "es-UY": {
            "name": "es-UY",
            "september": [
                "set",
                "setiembre"
            ]
        },
        "es-VE": {
            "name": "es-VE"
        }
    },
    "skip": [
        "a las",
        "cerca",
        "de",
        "del",
        "en",
        "y",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "pertain": [
        "de",
        "del"
    ],
    "sentence_splitter_group": 2,
    "ago": [
        "hace"
    ],
    "in": [
        "en"
    ],
    "simplifications": [
        {
            "una": "1"
        },
        {
            "un": "1"
        }
    ]
}
