#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断PaddleOCR问题的测试脚本
"""

import os
import sys
import traceback
import gc
import psutil

def check_system_resources():
    """检查系统资源"""
    print("=== 系统资源检查 ===")
    print(f"可用内存: {psutil.virtual_memory().available / (1024**3):.2f} GB")
    print(f"总内存: {psutil.virtual_memory().total / (1024**3):.2f} GB")
    print(f"内存使用率: {psutil.virtual_memory().percent}%")
    print(f"CPU核心数: {psutil.cpu_count()}")
    print()

def test_basic_imports():
    """测试基本导入"""
    print("=== 测试基本导入 ===")
    try:
        import paddle
        print(f"✓ Paddle版本: {paddle.__version__}")
    except Exception as e:
        print(f"✗ Paddle导入失败: {e}")
        return False
    
    try:
        from paddleocr import PaddleOCR
        print("✓ PaddleOCR导入成功")
    except Exception as e:
        print(f"✗ PaddleOCR导入失败: {e}")
        return False
    
    try:
        import fitz
        print(f"✓ PyMuPDF版本: {fitz.version}")
    except Exception as e:
        print(f"✗ PyMuPDF导入失败: {e}")
        return False
    
    print()
    return True

def test_paddleocr_init():
    """测试PaddleOCR初始化"""
    print("=== 测试PaddleOCR初始化 ===")
    try:
        from paddleocr import PaddleOCR
        
        print("正在初始化PaddleOCR（简化参数）...")
        ocr = PaddleOCR(lang='ch')
        print("✓ PaddleOCR初始化成功")
        
        # 强制垃圾回收
        del ocr
        gc.collect()
        
        return True
    except Exception as e:
        print(f"✗ PaddleOCR初始化失败: {e}")
        traceback.print_exc()
        return False

def test_pdf_conversion():
    """测试PDF转换"""
    print("=== 测试PDF转换 ===")
    
    test_folder = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张"
    test_file = "广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf"
    test_path = os.path.join(test_folder, test_file)
    
    if not os.path.exists(test_path):
        print(f"✗ 测试文件不存在: {test_path}")
        return False
    
    try:
        import fitz
        print(f"正在转换PDF: {test_file}")
        
        doc = fitz.open(test_path)
        page = doc[0]
        
        # 使用较小的缩放比例以节省内存
        mat = fitz.Matrix(1.5, 1.5)
        pix = page.get_pixmap(matrix=mat)
        
        temp_image = "diagnostic_test.png"
        pix.save(temp_image)
        doc.close()
        
        print(f"✓ PDF转换成功: {temp_image}")
        print(f"图片大小: {os.path.getsize(temp_image) / (1024**2):.2f} MB")
        
        return temp_image
    except Exception as e:
        print(f"✗ PDF转换失败: {e}")
        traceback.print_exc()
        return None

def test_ocr_simple(image_path):
    """测试简单OCR识别"""
    print("=== 测试OCR识别 ===")
    
    if not image_path or not os.path.exists(image_path):
        print("✗ 图片文件不存在")
        return False
    
    try:
        from paddleocr import PaddleOCR
        
        print("初始化OCR（最简参数）...")
        ocr = PaddleOCR(lang='ch')
        
        print("开始OCR识别...")
        print(f"当前内存使用: {psutil.virtual_memory().percent}%")
        
        result = ocr.predict(image_path)
        
        print("✓ OCR识别完成")
        
        if result and len(result) > 0:
            page_result = result[0]
            texts = page_result.get('rec_texts', [])
            print(f"识别到 {len(texts)} 个文本区域")
            
            # 只显示前5个结果
            for i, text in enumerate(texts[:5]):
                print(f"  {i+1}. {text}")
        
        # 清理资源
        del ocr
        gc.collect()
        
        return True
        
    except Exception as e:
        print(f"✗ OCR识别失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("PaddleOCR诊断测试开始")
    print("=" * 50)
    
    # 检查系统资源
    check_system_resources()
    
    # 测试导入
    if not test_basic_imports():
        print("基本导入测试失败，退出")
        return
    
    # 测试PaddleOCR初始化
    if not test_paddleocr_init():
        print("PaddleOCR初始化测试失败，退出")
        return
    
    # 测试PDF转换
    image_path = test_pdf_conversion()
    if not image_path:
        print("PDF转换测试失败，退出")
        return
    
    # 测试OCR识别
    if test_ocr_simple(image_path):
        print("\n✓ 所有测试通过！")
    else:
        print("\n✗ OCR识别测试失败")
    
    # 清理临时文件
    if image_path and os.path.exists(image_path):
        try:
            os.remove(image_path)
            print(f"清理临时文件: {image_path}")
        except:
            pass

if __name__ == "__main__":
    main()
