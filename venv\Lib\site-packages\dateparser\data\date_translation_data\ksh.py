info = {
    "name": "ksh",
    "date_order": "DMY",
    "january": [
        "jan",
        "jannewa"
    ],
    "february": [
        "fäb",
        "fäbrowa"
    ],
    "march": [
        "mäz",
        "määz"
    ],
    "april": [
        "apr",
        "aprell"
    ],
    "may": [
        "mai"
    ],
    "june": [
        "jun",
        "juuni"
    ],
    "july": [
        "jul",
        "juuli"
    ],
    "august": [
        "ouj",
        "oujoß"
    ],
    "september": [
        "septämber",
        "säp"
    ],
    "october": [
        "okt",
        "oktohber"
    ],
    "november": [
        "nov",
        "novämber"
    ],
    "december": [
        "dez",
        "dezämber"
    ],
    "monday": [
        "mo",
        "mohndaach"
    ],
    "tuesday": [
        "di",
        "dinnsdaach"
    ],
    "wednesday": [
        "me",
        "metwoch"
    ],
    "thursday": [
        "du",
        "dunnersdaach"
    ],
    "friday": [
        "fr",
        "friidaach"
    ],
    "saturday": [
        "sa",
        "samsdaach"
    ],
    "sunday": [
        "su",
        "sunndaach"
    ],
    "am": [
        "uhr vörmiddaachs",
        "vm",
        "vörmeddaach"
    ],
    "pm": [
        "nm",
        "nommendaach",
        "uhr nommendaachs"
    ],
    "year": [
        "j",
        "johr"
    ],
    "month": [
        "m",
        "mohnd"
    ],
    "week": [
        "w",
        "woch"
    ],
    "day": [
        "d",
        "daach"
    ],
    "hour": [
        "s",
        "schtund",
        "std"
    ],
    "minute": [
        "m",
        "menutt",
        "min"
    ],
    "second": [
        "s",
        "sek",
        "sekond"
    ],
    "relative-type": {
        "0 day ago": [
            "hück"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "diese mohnd"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "di woch"
        ],
        "0 year ago": [
            "diß johr"
        ],
        "1 day ago": [
            "jestere"
        ],
        "1 month ago": [
            "lätzde mohnd"
        ],
        "1 week ago": [
            "läz woch"
        ],
        "1 year ago": [
            "läz johr"
        ],
        "in 1 day": [
            "morje"
        ],
        "in 1 month": [
            "nächste mohnd"
        ],
        "in 1 week": [
            "nächste woche"
        ],
        "in 1 year": [
            "näx johr"
        ]
    },
    "relative-type-regex": {
        "\\1 year ago": [
            "vör (\\d+[.,]?\\d*) johr",
            "vör (\\d+[.,]?\\d*) johre"
        ],
        "in \\1 year": [
            "en (\\d+[.,]?\\d*) johr",
            "en (\\d+[.,]?\\d*) johre"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
