info = {
    "name": "hu",
    "date_order": "<PERSON><PERSON>",
    "january": [
        "jan",
        "janu<PERSON>r",
        "<PERSON>"
    ],
    "february": [
        "febr",
        "febru<PERSON>r",
        "feb",
        "II"
    ],
    "march": [
        "márc",
        "m<PERSON><PERSON><PERSON>",
        "már",
        "III"
    ],
    "april": [
        "ápr",
        "április",
        "IV"
    ],
    "may": [
        "máj",
        "május",
        "V"
    ],
    "june": [
        "jún",
        "június",
        "VI"
    ],
    "july": [
        "júl",
        "július",
        "VII"
    ],
    "august": [
        "aug",
        "augusztus",
        "VIII"
    ],
    "september": [
        "szept",
        "szeptember",
        "IX"
    ],
    "october": [
        "okt",
        "október",
        "X"
    ],
    "november": [
        "nov",
        "november",
        "XI"
    ],
    "december": [
        "dec",
        "december",
        "XII"
    ],
    "monday": [
        "h",
        "hétfő"
    ],
    "tuesday": [
        "k",
        "kedd"
    ],
    "wednesday": [
        "sze",
        "szerda"
    ],
    "thursday": [
        "cs",
        "csütörtök"
    ],
    "friday": [
        "p",
        "péntek"
    ],
    "saturday": [
        "szo",
        "szombat"
    ],
    "sunday": [
        "v",
        "vasárnap",
        "vas"
    ],
    "am": [
        "de"
    ],
    "pm": [
        "du"
    ],
    "year": [
        "év",
        "évek",
        "évvel",
        "éve"
    ],
    "month": [
        "hónap",
        "hó",
        "hónapok",
        "hónappal",
        "hónapja"
    ],
    "week": [
        "hét",
        "hetek",
        "héttel",
        "hete"
    ],
    "day": [
        "nap",
        "napok",
        "napja",
        "nappal"
    ],
    "hour": [
        "óra",
        "ó",
        "órák",
        "órája",
        "órával",
        "óráig",
        "órától"
    ],
    "minute": [
        "perc",
        "p",
        "percek",
        "perce",
        "perccel",
        "percig",
        "perctől"
    ],
    "second": [
        "másodperc",
        "mp",
        "másodpercek",
        "másodperce",
        "másodperccel",
        "másodpercig",
        "másodperctől"
    ],
    "relative-type": {
        "0 day ago": [
            "ma"
        ],
        "0 hour ago": [
            "ebben az órában"
        ],
        "0 minute ago": [
            "ebben a percben"
        ],
        "0 month ago": [
            "ez a hónap"
        ],
        "0 second ago": [
            "most"
        ],
        "0 week ago": [
            "ez a hét"
        ],
        "0 year ago": [
            "ez az év"
        ],
        "1 day ago": [
            "tegnap"
        ],
        "1 month ago": [
            "előző hónap"
        ],
        "1 week ago": [
            "előző hét"
        ],
        "1 year ago": [
            "előző év"
        ],
        "in 1 day": [
            "holnap"
        ],
        "in 1 month": [
            "következő hónap"
        ],
        "in 1 week": [
            "következő hét"
        ],
        "in 1 year": [
            "következő év"
        ],
        "2 day ago": [
            "tegnapelőtt"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) napja",
            "(\\d+[.,]?\\d*) nappal ezelőtt"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) órával ezelőtt"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) perccel ezelőtt"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) hónappal ezelőtt"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) másodperccel ezelőtt"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) héttel ezelőtt"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) évvel ezelőtt"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) nap múlva"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) óra múlva"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) perc múlva"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) hónap múlva"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) másodperc múlva"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) hét múlva"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) év múlva"
        ]
    },
    "locale_specific": {},
    "skip": [
        "-a",
        "-ai",
        "-akor",
        "-e",
        "-ei",
        "-ekor",
        "-es",
        "-i",
        "-ig",
        "-je",
        "-jei",
        "-ji",
        "-kor",
        "-tól",
        "-től",
        "-áig",
        "-án",
        "-ától",
        "-éig",
        "-én",
        "-étől",
        "-ös",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "ezelőtt"
    ],
    "in": [
        "múlva"
    ],
    "simplifications": [
        {
            "egy": "1"
        }
    ]
}
