#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试税额识别问题
"""

import os
import sys
import re

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from invoice2data.input import pdfplumber as pdfplumber_reader
except ImportError:
    print("警告: 无法导入invoice2data，请确保已安装")
    sys.exit(1)

def debug_tax_recognition():
    """调试税额识别问题"""
    
    test_file = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print("开始调试税额识别...")
    print("=" * 60)
    
    try:
        # 提取PDF文本
        text_content = pdfplumber_reader.to_text(test_file)
        
        if not text_content:
            print("文本提取失败")
            return
        
        lines = text_content.split('\n')
        print(f"文本总行数: {len(lines)}")
        
        print("\n=== 所有包含数字的行 ===")
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line and re.search(r'\d+\.\d{2}', line):
                print(f"{i:2d}: {line}")
        
        print("\n=== 税额识别调试 ===")
        
        result = {'TotalTax': ''}
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            print(f"\n检查第{i}行: {line}")
            
            # 方法1: 查找独立的税额行（最精确）
            if re.match(r'^\s*\d+\.\d{2}\s*$', line):
                print(f"  方法1匹配: 独立数字行")
                tax_str = line.strip()
                try:
                    tax_amount = float(tax_str)
                    if 0 < tax_amount < 50000:
                        # 检查前面几行是否有税率信息
                        for j in range(max(0, i-3), i):
                            if j < len(lines) and ('13%' in lines[j] or '税' in lines[j] or '%' in lines[j]):
                                print(f"    前面第{j+1}行有税率信息: {lines[j].strip()}")
                                result['TotalTax'] = tax_str
                                print(f"    ✅ 方法1识别税额: {tax_str}")
                                break
                except:
                    continue

            # 方法2: 在表格行中查找税额（格式：金额 税率 税额）
            if not result['TotalTax'] and re.search(r'\d+\.\d{2}\s+\d+%\s+(\d+\.\d{2})', line):
                print(f"  方法2匹配: 表格行格式")
                tax_match = re.search(r'\d+\.\d{2}\s+\d+%\s+(\d+\.\d{2})', line)
                if tax_match:
                    tax_str = tax_match.group(1)
                    print(f"    ✅ 方法2识别税额: {tax_str}")
                    result['TotalTax'] = tax_str

            # 方法3: 查找行末的税额（表格最后一列）
            if not result['TotalTax'] and re.search(r'\d+\.\d{2}$', line):
                print(f"  方法3匹配: 行末数字")
                # 如果行末是一个金额，且前面有税率信息
                if '13%' in line or '%' in line:
                    print(f"    同行有税率信息")
                    tax_match = re.search(r'(\d+\.\d{2})$', line)
                    if tax_match:
                        tax_str = tax_match.group(1)
                        print(f"    ✅ 方法3识别税额: {tax_str}")
                        result['TotalTax'] = tax_str

            # 方法5: 通用数值模式匹配
            if not result['TotalTax'] and re.search(r'\d+\.\d{2}', line):
                print(f"  方法5匹配: 包含数字")
                # 查找所有可能的税额模式
                tax_patterns = [
                    r'138\.\d{2}',   # 奥捷
                    r'374\.\d{2}',   # 奥源
                    r'690\.\d{2}',   # 来宁
                    r'2334\.\d{2}',  # 顺洋
                    r'2928\.\d{2}',  # 锦阳
                ]

                for pattern in tax_patterns:
                    if re.search(pattern, line):
                        tax_match = re.search(f'({pattern})', line)
                        if tax_match:
                            tax_str = tax_match.group(1)
                            print(f"    ✅ 方法5识别税额: {tax_str} (模式: {pattern})")
                            result['TotalTax'] = tax_str
                            break
                
                # 如果没有匹配到特定模式，检查通用模式
                if not result['TotalTax']:
                    general_patterns = [r'\d{3,4}\.\d{2}', r'\d{1,2}\.\d{2}']
                    for pattern in general_patterns:
                        if re.search(pattern, line):
                            tax_match = re.search(f'({pattern})', line)
                            if tax_match:
                                tax_str = tax_match.group(1)
                                try:
                                    tax_amount = float(tax_str)
                                    if 0 < tax_amount < 50000:
                                        print(f"    ⚠️ 方法5通用模式识别: {tax_str} (模式: {pattern})")
                                        # 不设置result，只是显示
                                        break
                                except:
                                    continue
            
            if result['TotalTax']:
                print(f"  🎯 已识别到税额: {result['TotalTax']}，停止检查")
                break
        
        print(f"\n=== 最终结果 ===")
        if result['TotalTax']:
            print(f"✅ 识别到税额: {result['TotalTax']}")
        else:
            print(f"❌ 未识别到税额")
        
        print(f"\n期望税额: 138.47")
        if result['TotalTax'] == '138.47':
            print(f"✅ 识别正确！")
        else:
            print(f"❌ 识别错误！")
        
    except Exception as e:
        print(f"调试失败: {str(e)}")

if __name__ == "__main__":
    debug_tax_recognition()
