info = {
    "name": "da",
    "date_order": "DMY",
    "january": [
        "jan",
        "januar"
    ],
    "february": [
        "feb",
        "februar"
    ],
    "march": [
        "mar",
        "marts"
    ],
    "april": [
        "apr",
        "april"
    ],
    "may": [
        "maj"
    ],
    "june": [
        "jun",
        "juni"
    ],
    "july": [
        "jul",
        "juli"
    ],
    "august": [
        "aug",
        "august"
    ],
    "september": [
        "sep",
        "september"
    ],
    "october": [
        "okt",
        "oktober"
    ],
    "november": [
        "nov",
        "november"
    ],
    "december": [
        "dec",
        "december"
    ],
    "monday": [
        "man",
        "mandag"
    ],
    "tuesday": [
        "tir",
        "tirsdag"
    ],
    "wednesday": [
        "ons",
        "onsdag"
    ],
    "thursday": [
        "tor",
        "torsdag"
    ],
    "friday": [
        "fre",
        "fredag"
    ],
    "saturday": [
        "lør",
        "lørdag"
    ],
    "sunday": [
        "søn",
        "søndag"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "år"
    ],
    "month": [
        "md",
        "måned",
        "måneder"
    ],
    "week": [
        "uge",
        "uger"
    ],
    "day": [
        "dag",
        "dage"
    ],
    "hour": [
        "t",
        "time",
        "timer"
    ],
    "minute": [
        "min",
        "minut",
        "minutter"
    ],
    "second": [
        "s",
        "sek",
        "sekund",
        "sekunder"
    ],
    "relative-type": {
        "0 day ago": [
            "i dag"
        ],
        "0 hour ago": [
            "i den kommende time"
        ],
        "0 minute ago": [
            "i det kommende minut"
        ],
        "0 month ago": [
            "denne md",
            "denne måned"
        ],
        "0 second ago": [
            "nu"
        ],
        "0 week ago": [
            "denne uge"
        ],
        "0 year ago": [
            "i år"
        ],
        "1 day ago": [
            "i går"
        ],
        "1 month ago": [
            "sidste md",
            "sidste måned"
        ],
        "1 week ago": [
            "sidste uge"
        ],
        "1 year ago": [
            "sidste år"
        ],
        "in 1 day": [
            "i morgen"
        ],
        "in 1 month": [
            "næste md",
            "næste måned"
        ],
        "in 1 week": [
            "næste uge"
        ],
        "in 1 year": [
            "næste år"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "for (\\d+[.,]?\\d*) dag siden",
            "for (\\d+[.,]?\\d*) dage siden"
        ],
        "\\1 hour ago": [
            "for (\\d+[.,]?\\d*) time siden",
            "for (\\d+[.,]?\\d*) timer siden",
            "for (\\d+[.,]?\\d*)\\s*h",
            "for (\\d+[.,]?\\d*) timer"
        ],
        "\\1 minute ago": [
            "for (\\d+[.,]?\\d*) min siden",
            "for (\\d+[.,]?\\d*) minut siden",
            "for (\\d+[.,]?\\d*) minutter siden",
            "for (\\d+[.,]?\\d*)\\s*m",
            "for (\\d+[.,]?\\d*) minutter"
        ],
        "\\1 month ago": [
            "for (\\d+[.,]?\\d*) md siden",
            "for (\\d+[.,]?\\d*) mdr siden",
            "for (\\d+[.,]?\\d*) måned siden",
            "for (\\d+[.,]?\\d*) måneder siden"
        ],
        "\\1 second ago": [
            "for (\\d+[.,]?\\d*) sek siden",
            "for (\\d+[.,]?\\d*) sekund siden",
            "for (\\d+[.,]?\\d*) sekunder siden",
            "for (\\d+[.,]?\\d*)\\s*s",
            "for (\\d+[.,]?\\d*) sekunder"
        ],
        "\\1 week ago": [
            "for (\\d+[.,]?\\d*) uge siden",
            "for (\\d+[.,]?\\d*) uger siden"
        ],
        "\\1 year ago": [
            "for (\\d+[.,]?\\d*) år siden"
        ],
        "in \\1 day": [
            "om (\\d+[.,]?\\d*) dag",
            "om (\\d+[.,]?\\d*) dage"
        ],
        "in \\1 hour": [
            "om (\\d+[.,]?\\d*) time",
            "om (\\d+[.,]?\\d*) timer"
        ],
        "in \\1 minute": [
            "om (\\d+[.,]?\\d*) min",
            "om (\\d+[.,]?\\d*) minut",
            "om (\\d+[.,]?\\d*) minutter"
        ],
        "in \\1 month": [
            "om (\\d+[.,]?\\d*) md",
            "om (\\d+[.,]?\\d*) mdr",
            "om (\\d+[.,]?\\d*) måned",
            "om (\\d+[.,]?\\d*) måneder"
        ],
        "in \\1 second": [
            "om (\\d+[.,]?\\d*) sek",
            "om (\\d+[.,]?\\d*) sekund",
            "om (\\d+[.,]?\\d*) sekunder"
        ],
        "in \\1 week": [
            "om (\\d+[.,]?\\d*) uge",
            "om (\\d+[.,]?\\d*) uger"
        ],
        "in \\1 year": [
            "om (\\d+[.,]?\\d*) år"
        ]
    },
    "locale_specific": {
        "da-GL": {
            "name": "da-GL"
        }
    },
    "skip": [
        "cirka",
        "d.",
        "kl",
        "kl.",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "siden"
    ],
    "in": [
        "i"
    ],
    "simplifications": [
        {
            "en": "1"
        },
        {
            "et": "1"
        },
        {
            "(\\d+[.,]?\\d*)\\s*hr(s?)": "\\1 time\\2"
        },
        {
            "(\\d+[.,]?\\d*)\\s*min(s?)": "\\1 minut\\2"
        },
        {
            "(\\d+[.,]?\\d*)\\s*sec(s?)": "\\1 sekund\\2"
        },
        {
            "middag": "12:00"
        },
        {
            "midnat": "00:00"
        },
        {
            "(\\d+[.,]?\\d*)h(\\d+[.,]?\\d*)m?": "\\1:\\2"
        },
        {
            "mindre end 1 minut siden": "45 seconds"
        }
    ]
}
