#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试Excel处理功能
"""

import win32com.client
from datetime import datetime
import os
import traceback

def debug_excel_processing():
    """调试Excel处理功能"""
    
    # 测试文件路径
    template_path = r"D:\vscode project\fpcl\1\模板.xls"
    export_list_path = r"D:\vscode project\fpcl\1\202501导出清单.xlsx"
    
    print("开始调试Excel处理...")
    print(f"模板文件: {template_path}")
    print(f"导出清单: {export_list_path}")
    
    # 检查文件是否存在
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    if not os.path.exists(export_list_path):
        print(f"❌ 导出清单不存在: {export_list_path}")
        return
    
    print("✅ 文件存在检查通过")
    
    # 生成新文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    keywords_str = "测试"
    new_template_name = f"{keywords_str}报销表_{timestamp}.xlsx"
    new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)
    
    print(f"新文件路径: {new_template_path}")
    
    # 检查目标文件夹权限
    target_dir = os.path.dirname(template_path)
    if not os.access(target_dir, os.W_OK):
        print(f"❌ 目标文件夹没有写权限: {target_dir}")
        return
    
    print("✅ 文件夹权限检查通过")
    
    # 尝试Excel操作
    excel = None
    wb_template = None
    wb = None
    
    try:
        print("正在启动Excel应用程序...")
        excel = win32com.client.Dispatch('Excel.Application')
        excel.Visible = False
        excel.DisplayAlerts = False
        print("✅ Excel应用程序启动成功")
        
        print("正在打开模板文件...")
        wb_template = excel.Workbooks.Open(template_path)
        print("✅ 模板文件打开成功")
        
        print("正在另存为新文件...")
        wb_template.SaveAs(
            Filename=new_template_path,
            FileFormat=51,  # 51 = xlsx格式
            CreateBackup=False
        )
        print("✅ 新文件创建成功")
        
        wb_template.Close()
        print("✅ 模板文件已关闭")
        
        print("正在重新打开新文件...")
        wb = excel.Workbooks.Open(new_template_path)
        print("✅ 新文件打开成功")
        
        # 简单的测试操作
        ws = wb.Worksheets(1)
        ws.Cells(1, 1).Value = f"测试时间: {datetime.now()}"
        print("✅ 测试数据写入成功")
        
        print("正在保存文件...")
        wb.Save()
        print("✅ 文件保存成功")
        
        # 检查文件是否真的存在
        if os.path.exists(new_template_path):
            file_size = os.path.getsize(new_template_path)
            print(f"✅ 文件确实存在，大小: {file_size} 字节")
        else:
            print("❌ 文件保存后不存在")
        
    except Exception as e:
        print(f"❌ Excel操作失败: {str(e)}")
        traceback.print_exc()
        
    finally:
        try:
            if wb:
                print("正在关闭工作簿...")
                wb.Close()
                print("✅ 工作簿已关闭")
        except Exception as e:
            print(f"关闭工作簿时出错: {str(e)}")
        
        try:
            if excel:
                print("正在退出Excel应用程序...")
                excel.Quit()
                print("✅ Excel应用程序已退出")
        except Exception as e:
            print(f"退出Excel时出错: {str(e)}")

def test_simple_excel_operation():
    """测试简单的Excel操作"""
    
    print("测试简单的Excel操作...")
    
    # 创建一个简单的测试文件
    test_file_path = r"D:\vscode project\fpcl\1\简单测试.xlsx"
    
    excel = None
    wb = None
    
    try:
        print("启动Excel...")
        excel = win32com.client.Dispatch('Excel.Application')
        excel.Visible = False
        excel.DisplayAlerts = False
        
        print("创建新工作簿...")
        wb = excel.Workbooks.Add()
        
        print("写入测试数据...")
        ws = wb.Worksheets(1)
        ws.Cells(1, 1).Value = "测试"
        ws.Cells(1, 2).Value = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"保存到: {test_file_path}")
        wb.SaveAs(
            Filename=test_file_path,
            FileFormat=51,  # xlsx格式
            CreateBackup=False
        )
        
        print("检查文件是否存在...")
        if os.path.exists(test_file_path):
            file_size = os.path.getsize(test_file_path)
            print(f"✅ 测试成功！文件大小: {file_size} 字节")
        else:
            print("❌ 测试失败！文件不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        traceback.print_exc()
        
    finally:
        try:
            if wb:
                wb.Close()
            if excel:
                excel.Quit()
        except:
            pass

if __name__ == "__main__":
    print("Excel处理调试工具")
    print("=" * 50)
    
    # 先测试简单操作
    test_simple_excel_operation()
    
    print("\n" + "=" * 50)
    
    # 再测试完整操作
    debug_excel_processing()
