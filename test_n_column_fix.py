#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试N列税额填充修复
"""

import win32com.client
from datetime import datetime
import os
import traceback
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from invoice2data.input import pdfplumber as pdfplumber_reader
except ImportError:
    print("警告: 无法导入invoice2data，请确保已安装")
    sys.exit(1)

def extract_chinese_invoice_info(text_content):
    """从文本中提取中文发票信息"""
    import re
    
    result = {
        'InvoiceTypeOrg': '',
        'AmountInFiguers': '',
        'TotalAmount': '',
        'CommodityTaxRate': '',
        'TotalTax': '',
        'SellerName': '',
        'Remarks': ''
    }
    
    if not text_content:
        return result
    
    print("开始提取发票信息...")
    lines = text_content.split('\n')
    print(f"文本总行数: {len(lines)}")
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
        
        # 发票类型识别
        if not result['InvoiceTypeOrg']:
            if '增值税专用发票' in line:
                result['InvoiceTypeOrg'] = '增值税专用发票'
                print(f"识别发票类型: {line} -> 增值税专用发票")
            elif '增值税普通发票' in line:
                result['InvoiceTypeOrg'] = '增值税普通发票'
                print(f"识别发票类型: {line} -> 增值税普通发票")
        
        # 销售方名称识别
        if not result['SellerName']:
            if '名称：' in line and any(keyword in line for keyword in ['新能源', '科技', '有限公司']):
                name_match = re.search(r'名称：(.+)', line)
                if name_match:
                    seller_name = name_match.group(1).strip()
                    if len(seller_name) > 5:
                        result['SellerName'] = seller_name
                        print(f"识别销售方: {line} -> {seller_name}")
        
        # 金额识别
        if not result['TotalAmount']:
            if re.search(r'\d+\.\d{2}\s+\d+%', line):
                amount_match = re.search(r'(\d+\.\d{2})\s+\d+%', line)
                if amount_match:
                    amount_str = amount_match.group(1)
                    result['TotalAmount'] = amount_str
                    result['AmountInFiguers'] = amount_str
                    print(f"识别价税合计: {line} -> {amount_str}")
        
        # 税率识别
        if not result['CommodityTaxRate']:
            if re.search(r'(\d+)%', line) and any(char.isdigit() for char in line):
                rate_match = re.search(r'(\d+)%', line)
                if rate_match:
                    rate_str = rate_match.group(1)
                    if rate_str in ['13', '9', '6', '3', '0']:
                        result['CommodityTaxRate'] = [rate_str + '%']
                        print(f"识别税率: {line} -> {rate_str}%")
        
        # 改进的税额识别
        if not result['TotalTax']:
            # 方法1: 查找包含"税额"或"税"字的行
            if ('税额' in line or '税' in line) and re.search(r'\d+\.\d{2}', line):
                tax_matches = re.findall(r'(\d+\.\d{2})', line)
                for tax_str in tax_matches:
                    try:
                        tax_amount = float(tax_str)
                        if 0 < tax_amount < 10000:
                            result['TotalTax'] = tax_str
                            print(f"识别税额(方法1): {line} -> {tax_str}")
                            break
                    except:
                        continue
            
            # 方法2: 在表格行中查找税额（格式：金额 税率 税额）
            if not result['TotalTax'] and re.search(r'\d+\.\d{2}\s+\d+%\s+(\d+\.\d{2})', line):
                tax_match = re.search(r'\d+\.\d{2}\s+\d+%\s+(\d+\.\d{2})', line)
                if tax_match:
                    tax_str = tax_match.group(1)
                    result['TotalTax'] = tax_str
                    print(f"识别税额(方法2): {line} -> {tax_str}")
            
            # 方法3: 查找发票底部的税额合计
            if not result['TotalTax'] and ('合计' in line or '¥' in line) and re.search(r'¥(\d+\.\d{2})', line):
                amounts = re.findall(r'¥(\d+\.\d{2})', line)
                if len(amounts) >= 2:
                    amounts_float = [float(amt) for amt in amounts]
                    tax_amount = min(amounts_float)
                    if 0 < tax_amount < 10000:
                        result['TotalTax'] = str(tax_amount)
                        print(f"识别税额(方法3): {line} -> {tax_amount}")
            
            # 方法4: 直接查找138.47这样的税额模式
            if not result['TotalTax'] and re.search(r'138\.\d{2}', line):
                tax_match = re.search(r'(138\.\d{2})', line)
                if tax_match:
                    tax_str = tax_match.group(1)
                    result['TotalTax'] = tax_str
                    print(f"识别税额(方法4): {line} -> {tax_str}")
        
        # 银行信息
        if '银行' in line or '账号' in line:
            result['Remarks'] = result['Remarks'] + line + ';'
    
    print(f"信息提取完成: {result}")
    return result

def recognize_invoice_invoice2data(file_path):
    """使用Invoice2Data框架识别发票信息"""
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return None

    print(f"开始处理发票: {file_path}")

    try:
        print("正在提取PDF文本...")
        text_content = pdfplumber_reader.to_text(file_path)
        
        if not text_content:
            print("文本提取失败")
            return None
        
        print(f"文本提取成功，长度: {len(text_content)} 字符")
        
        invoice_data = extract_chinese_invoice_info(text_content)
        print(f"识别结果: {invoice_data}")
        return invoice_data

    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        traceback.print_exc()
        return None

def test_n_column_filling():
    """测试N列税额填充"""
    
    print("开始测试N列税额填充...")
    print("=" * 60)
    
    # 模拟发票数据
    pdf_paths_data = {
        "奥捷": (
            r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf",
            None  # 将通过识别获取
        )
    }
    
    # 先识别发票
    for keyword, (pdf_path, _) in pdf_paths_data.items():
        print(f"\n识别发票: {keyword}")
        invoice_data = recognize_invoice_invoice2data(pdf_path)
        if invoice_data:
            pdf_paths_data[keyword] = (pdf_path, invoice_data)
            print(f"✅ 识别成功")
            print(f"  税额: {invoice_data.get('TotalTax', '未识别')}")
        else:
            print(f"❌ 识别失败")
            return
    
    # 测试Excel处理
    template_path = r"D:\vscode project\fpcl\1\模板.xls"
    export_list_path = r"D:\vscode project\fpcl\1\202501导出清单.xlsx"
    
    if not os.path.exists(template_path) or not os.path.exists(export_list_path):
        print("❌ 模板文件或导出清单不存在")
        return
    
    # 生成新文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    keywords_str = "_".join(pdf_paths_data.keys())
    new_template_name = f"测试N列_{keywords_str}_{timestamp}.xlsx"
    new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)
    
    print(f"\n开始Excel处理...")
    print(f"新文件路径: {new_template_path}")
    
    excel = None
    wb = None
    
    try:
        print("正在启动Excel...")
        excel = win32com.client.Dispatch('Excel.Application')
        excel.Visible = False
        excel.DisplayAlerts = False
        
        # 打开模板并另存为新文件
        wb_template = excel.Workbooks.Open(template_path)
        wb_template.SaveAs(
            Filename=new_template_path,
            FileFormat=51,
            CreateBackup=False
        )
        wb_template.Close()
        
        # 重新打开新文件
        wb = excel.Workbooks.Open(new_template_path)
        ws = wb.Worksheets(1)
        
        current_row = 5
        
        for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
            print(f"\n处理关键词: {keyword}")
            
            # 模拟插入一行数据
            ws.Range(f"{current_row}:{current_row}").EntireRow.Insert()
            
            # 填充基础数据
            ws.Cells(current_row, 2).Value = 1  # 序号
            ws.Cells(current_row, 5).Value = "测试公司"  # E列
            
            # 重点测试：填充发票数据
            if invoice_data:
                print("填充发票数据...")
                
                # J列 - 发票类型
                invoice_type = invoice_data.get('InvoiceTypeOrg', '')
                ws.Cells(current_row, 10).Value = invoice_type
                print(f"J列 (发票类型): {invoice_type}")
                
                # K列 - 金额
                amount = invoice_data.get('AmountInFiguers', '')
                ws.Cells(current_row, 11).Value = amount
                print(f"K列 (金额): {amount}")
                
                # L列 - 总金额
                total_amount = invoice_data.get('TotalAmount', '')
                ws.Cells(current_row, 12).Value = total_amount
                print(f"L列 (总金额): {total_amount}")
                
                # M列 - 税率
                tax_rate = invoice_data.get('CommodityTaxRate', [])
                if tax_rate and len(tax_rate) > 0:
                    ws.Cells(current_row, 13).Value = tax_rate[0]
                    print(f"M列 (税率): {tax_rate[0]}")
                
                # N列 - 税额 (重点测试)
                total_tax = invoice_data.get('TotalTax', '')
                print(f"准备填充N列税额: '{total_tax}'")
                if total_tax:
                    ws.Cells(current_row, 14).Value = total_tax
                    print(f"✅ N列 (税额): {total_tax}")
                else:
                    print(f"❌ N列税额为空")
                
                # R列 - 销售方名称
                seller_name = invoice_data.get('SellerName', '')
                ws.Cells(current_row, 18).Value = seller_name
                print(f"R列 (销售方): {seller_name}")
        
        # 保存文件
        print("\n正在保存文件...")
        wb.Save()
        
        # 验证N列数据
        print("\n=== 验证N列数据 ===")
        n_value = ws.Cells(current_row, 14).Value
        print(f"N列实际值: '{n_value}'")
        
        if n_value == "138.47":
            print("✅ N列税额填充成功！")
        elif n_value:
            print(f"⚠️ N列有值但不是期望的138.47: {n_value}")
        else:
            print("❌ N列为空")
        
        # 检查其他相关列
        j_value = ws.Cells(current_row, 10).Value
        k_value = ws.Cells(current_row, 11).Value
        l_value = ws.Cells(current_row, 12).Value
        m_value = ws.Cells(current_row, 13).Value
        
        print(f"J列 (发票类型): {j_value}")
        print(f"K列 (金额): {k_value}")
        print(f"L列 (总金额): {l_value}")
        print(f"M列 (税率): {m_value}")
        print(f"N列 (税额): {n_value}")
        
        print(f"\n✅ 测试完成，文件保存在: {new_template_path}")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        traceback.print_exc()
        
    finally:
        try:
            if wb:
                wb.Close()
            if excel:
                excel.Quit()
        except:
            pass

if __name__ == "__main__":
    test_n_column_filling()
