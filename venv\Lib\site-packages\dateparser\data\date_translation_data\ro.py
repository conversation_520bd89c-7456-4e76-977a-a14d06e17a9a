info = {
    "name": "ro",
    "date_order": "DMY",
    "january": [
        "ian",
        "ianuarie"
    ],
    "february": [
        "feb",
        "februarie",
        "Febr"
    ],
    "march": [
        "mar",
        "martie",
        "Mart"
    ],
    "april": [
        "apr",
        "aprilie"
    ],
    "may": [
        "mai"
    ],
    "june": [
        "iun",
        "iunie"
    ],
    "july": [
        "iul",
        "iulie"
    ],
    "august": [
        "aug",
        "august"
    ],
    "september": [
        "sept",
        "septembrie",
        "Sep"
    ],
    "october": [
        "oct",
        "octombrie"
    ],
    "november": [
        "noiembrie",
        "nov",
        "Noiem"
    ],
    "december": [
        "dec",
        "decembrie"
    ],
    "monday": [
        "lun",
        "luni"
    ],
    "tuesday": [
        "mar",
        "marți"
    ],
    "wednesday": [
        "mie",
        "miercuri",
        "Mi"
    ],
    "thursday": [
        "joi"
    ],
    "friday": [
        "vin",
        "vineri"
    ],
    "saturday": [
        "sâm",
        "sâmbătă"
    ],
    "sunday": [
        "dum",
        "duminică"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "an",
        "ani"
    ],
    "month": [
        "lună",
        "luna",
        "luni"
    ],
    "week": [
        "săpt",
        "săptămână",
        "săptămâni"
    ],
    "day": [
        "zi",
        "zile"
    ],
    "hour": [
        "h",
        "oră",
        "ore"
    ],
    "minute": [
        "m",
        "min",
        "minut",
        "minute"
    ],
    "second": [
        "s",
        "sec",
        "secundă",
        "secunde"
    ],
    "relative-type": {
        "0 day ago": [
            "azi"
        ],
        "0 hour ago": [
            "ora aceasta"
        ],
        "0 minute ago": [
            "minutul acesta"
        ],
        "0 month ago": [
            "luna aceasta"
        ],
        "0 second ago": [
            "acum"
        ],
        "0 week ago": [
            "săptămâna aceasta"
        ],
        "0 year ago": [
            "anul acesta"
        ],
        "1 day ago": [
            "ieri"
        ],
        "1 month ago": [
            "luna trecută"
        ],
        "1 week ago": [
            "săptămâna trecută"
        ],
        "1 year ago": [
            "anul trecut"
        ],
        "in 1 day": [
            "mâine"
        ],
        "in 1 month": [
            "luna viitoare"
        ],
        "in 1 week": [
            "săptămâna viitoare"
        ],
        "in 1 year": [
            "anul viitor"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "acum (\\d+[.,]?\\d*) de zile",
            "acum (\\d+[.,]?\\d*) zi",
            "acum (\\d+[.,]?\\d*) zile"
        ],
        "\\1 hour ago": [
            "acum (\\d+[.,]?\\d*) de ore",
            "acum (\\d+[.,]?\\d*) h",
            "acum (\\d+[.,]?\\d*) oră"
        ],
        "\\1 minute ago": [
            "acum (\\d+[.,]?\\d*) de minute",
            "acum (\\d+[.,]?\\d*) min",
            "acum (\\d+[.,]?\\d*) minut"
        ],
        "\\1 month ago": [
            "acum (\\d+[.,]?\\d*) de luni",
            "acum (\\d+[.,]?\\d*) luni",
            "acum (\\d+[.,]?\\d*) lună"
        ],
        "\\1 second ago": [
            "acum (\\d+[.,]?\\d*) de secunde",
            "acum (\\d+[.,]?\\d*) sec",
            "acum (\\d+[.,]?\\d*) secundă"
        ],
        "\\1 week ago": [
            "acum (\\d+[.,]?\\d*) de săptămâni",
            "acum (\\d+[.,]?\\d*) săpt",
            "acum (\\d+[.,]?\\d*) săptămână"
        ],
        "\\1 year ago": [
            "acum (\\d+[.,]?\\d*) an",
            "acum (\\d+[.,]?\\d*) de ani"
        ],
        "in \\1 day": [
            "peste (\\d+[.,]?\\d*) de zile",
            "peste (\\d+[.,]?\\d*) zi",
            "peste (\\d+[.,]?\\d*) zile"
        ],
        "in \\1 hour": [
            "peste (\\d+[.,]?\\d*) de ore",
            "peste (\\d+[.,]?\\d*) h",
            "peste (\\d+[.,]?\\d*) oră"
        ],
        "in \\1 minute": [
            "peste (\\d+[.,]?\\d*) de minute",
            "peste (\\d+[.,]?\\d*) min",
            "peste (\\d+[.,]?\\d*) minut"
        ],
        "in \\1 month": [
            "peste (\\d+[.,]?\\d*) de luni",
            "peste (\\d+[.,]?\\d*) luni",
            "peste (\\d+[.,]?\\d*) lună"
        ],
        "in \\1 second": [
            "peste (\\d+[.,]?\\d*) de secunde",
            "peste (\\d+[.,]?\\d*) sec",
            "peste (\\d+[.,]?\\d*) secundă"
        ],
        "in \\1 week": [
            "peste (\\d+[.,]?\\d*) de săptămâni",
            "peste (\\d+[.,]?\\d*) săpt",
            "peste (\\d+[.,]?\\d*) săptămână"
        ],
        "in \\1 year": [
            "peste (\\d+[.,]?\\d*) an",
            "peste (\\d+[.,]?\\d*) ani",
            "peste (\\d+[.,]?\\d*) de ani"
        ]
    },
    "locale_specific": {
        "ro-MD": {
            "name": "ro-MD"
        }
    },
    "skip": [
        "de",
        "la",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "în urmă"
    ],
    "in": [
        "în"
    ]
}
