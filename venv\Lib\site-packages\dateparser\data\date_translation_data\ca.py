info = {
    "name": "ca",
    "date_order": "DMY",
    "january": [
        "de gen",
        "de gener",
        "gen",
        "gener"
    ],
    "february": [
        "de febr",
        "de febrer",
        "febr",
        "febrer"
    ],
    "march": [
        "de març",
        "març"
    ],
    "april": [
        "abr",
        "abril",
        "d'abr",
        "d'abril"
    ],
    "may": [
        "de maig",
        "maig"
    ],
    "june": [
        "de juny",
        "juny"
    ],
    "july": [
        "de jul",
        "de juliol",
        "jul",
        "juliol"
    ],
    "august": [
        "ag",
        "agost",
        "d'ag",
        "d'agost"
    ],
    "september": [
        "de set",
        "de setembre",
        "set",
        "setembre"
    ],
    "october": [
        "d'oct",
        "d'octubre",
        "oct",
        "octubre"
    ],
    "november": [
        "de nov",
        "de novembre",
        "nov",
        "novembre"
    ],
    "december": [
        "de des",
        "de desembre",
        "des",
        "desembre"
    ],
    "monday": [
        "dilluns",
        "dl"
    ],
    "tuesday": [
        "dimarts",
        "dt"
    ],
    "wednesday": [
        "dc",
        "dimecres"
    ],
    "thursday": [
        "dijous",
        "dj"
    ],
    "friday": [
        "divendres",
        "dv"
    ],
    "saturday": [
        "dissabte",
        "ds"
    ],
    "sunday": [
        "dg",
        "diumenge"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "any"
    ],
    "month": [
        "mes"
    ],
    "week": [
        "setm",
        "setmana"
    ],
    "day": [
        "dia"
    ],
    "hour": [
        "h",
        "hora"
    ],
    "minute": [
        "min",
        "minut"
    ],
    "second": [
        "s",
        "segon"
    ],
    "relative-type": {
        "0 day ago": [
            "avui",
            "hui"
        ],
        "0 hour ago": [
            "aquesta hora"
        ],
        "0 minute ago": [
            "aquest minut"
        ],
        "0 month ago": [
            "aquest mes"
        ],
        "0 second ago": [
            "ara"
        ],
        "0 week ago": [
            "aquesta setm",
            "aquesta setmana"
        ],
        "0 year ago": [
            "enguany"
        ],
        "1 day ago": [
            "ahir"
        ],
        "1 month ago": [
            "el mes passat",
            "mes passat"
        ],
        "1 week ago": [
            "la setm passada",
            "la setmana passada",
            "setm passada"
        ],
        "1 year ago": [
            "l'any passat"
        ],
        "in 1 day": [
            "demà"
        ],
        "in 1 month": [
            "el mes que ve",
            "mes vinent"
        ],
        "in 1 week": [
            "la setm que ve",
            "la setmana que ve",
            "setm vinent",
            "la setmana vinent",
            "la pròxima setmana",
            "la propera setmana"
        ],
        "in 1 year": [
            "l'any que ve"
        ],
        "2 day ago": [
            "despús-ahir",
            "abans-d’ahir",
            "dellà-ahir"
        ],
        "in 2 day": [
            "endemà",
            "sendemà",
            "despús-demà",
            "demà passat",
            "passat demà"
        ],
        "in 3 day": [
            "endemà passat"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "fa (\\d+[.,]?\\d*) dia",
            "fa (\\d+[.,]?\\d*) dies"
        ],
        "\\1 hour ago": [
            "fa (\\d+[.,]?\\d*) h",
            "fa (\\d+[.,]?\\d*) hora",
            "fa (\\d+[.,]?\\d*) hores"
        ],
        "\\1 minute ago": [
            "fa (\\d+[.,]?\\d*) min",
            "fa (\\d+[.,]?\\d*) minut",
            "fa (\\d+[.,]?\\d*) minuts"
        ],
        "\\1 month ago": [
            "fa (\\d+[.,]?\\d*) mes",
            "fa (\\d+[.,]?\\d*) mesos"
        ],
        "\\1 second ago": [
            "fa (\\d+[.,]?\\d*) s",
            "fa (\\d+[.,]?\\d*) segon",
            "fa (\\d+[.,]?\\d*) segons"
        ],
        "\\1 week ago": [
            "fa (\\d+[.,]?\\d*) setm",
            "fa (\\d+[.,]?\\d*) setmana",
            "fa (\\d+[.,]?\\d*) setmanes"
        ],
        "\\1 year ago": [
            "fa (\\d+[.,]?\\d*) any",
            "fa (\\d+[.,]?\\d*) anys"
        ],
        "in \\1 day": [
            "d'aquí a (\\d+[.,]?\\d*) dia",
            "d'aquí a (\\d+[.,]?\\d*) dies"
        ],
        "in \\1 hour": [
            "d'aquí a (\\d+[.,]?\\d*) h",
            "d'aquí a (\\d+[.,]?\\d*) hora",
            "d'aquí a (\\d+[.,]?\\d*) hores",
            "d‘aquí a (\\d+[.,]?\\d*) h"
        ],
        "in \\1 minute": [
            "d'aquí a (\\d+[.,]?\\d*) min",
            "d'aquí a (\\d+[.,]?\\d*) minut",
            "d'aquí a (\\d+[.,]?\\d*) minuts"
        ],
        "in \\1 month": [
            "d'aquí a (\\d+[.,]?\\d*) mes",
            "d'aquí a (\\d+[.,]?\\d*) mesos"
        ],
        "in \\1 second": [
            "d'aquí a (\\d+[.,]?\\d*) s",
            "d'aquí a (\\d+[.,]?\\d*) segon",
            "d'aquí a (\\d+[.,]?\\d*) segons"
        ],
        "in \\1 week": [
            "d'aquí a (\\d+[.,]?\\d*) setm",
            "d'aquí a (\\d+[.,]?\\d*) setmana",
            "d'aquí a (\\d+[.,]?\\d*) setmanes"
        ],
        "in \\1 year": [
            "d'aquí a (\\d+[.,]?\\d*) any",
            "d'aquí a (\\d+[.,]?\\d*) anys"
        ]
    },
    "locale_specific": {
        "ca-AD": {
            "name": "ca-AD"
        },
        "ca-FR": {
            "name": "ca-FR"
        },
        "ca-IT": {
            "name": "ca-IT"
        }
    },
    "skip": [
        "de",
        "del",
        "i",
        "l'",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "pertain": [
        "de",
        "del"
    ],
    "in": [
        "en"
    ],
    "simplifications": [
        {
            "una": "1"
        },
        {
            "un": "1"
        }
    ]
}
