#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试价税合计识别问题
"""

import os
import sys
import re

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from invoice2data.input import pdfplumber as pdfplumber_reader
except ImportError:
    print("警告: 无法导入invoice2data，请确保已安装")
    sys.exit(1)

def debug_amount_recognition():
    """调试价税合计识别问题"""
    
    test_file = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print("开始调试价税合计识别...")
    print("=" * 60)
    
    try:
        # 提取PDF文本
        text_content = pdfplumber_reader.to_text(test_file)
        
        if not text_content:
            print("文本提取失败")
            return
        
        lines = text_content.split('\n')
        print(f"文本总行数: {len(lines)}")
        
        print("\n=== 所有包含金额符号的行 ===")
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line and ('¥' in line or '￥' in line or re.search(r'\d+\.\d{2}', line)):
                print(f"{i:2d}: {line}")
        
        print("\n=== 价税合计识别调试 ===")
        
        result = {'TotalAmount': '', 'AmountInFiguers': ''}
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if not line:
                continue
            
            print(f"\n检查第{i}行: {line}")
            
            # 方法1: 优先查找"价税合计"或"小写"后面的金额（最准确）
            if ('价税合计' in line or '小写' in line) and re.search(r'¥(\d+\.\d{2})', line):
                print(f"  方法1匹配: 价税合计或小写行")
                amount_match = re.search(r'¥(\d+\.\d{2})', line)
                if amount_match:
                    amount_str = amount_match.group(1)
                    try:
                        amount_val = float(amount_str)
                        if amount_val > 100:  # 价税合计应该是一个合理的金额
                            result['TotalAmount'] = amount_str
                            result['AmountInFiguers'] = amount_str
                            print(f"    ✅ 方法1识别价税合计: {amount_str}")
                    except:
                        continue
            
            # 方法2: 查找合计行中的金额
            elif not result['TotalAmount'] and '合计' in line and re.search(r'¥(\d+\.\d{2})', line):
                print(f"  方法2匹配: 合计行")
                amount_match = re.search(r'¥(\d+\.\d{2})', line)
                if amount_match:
                    amount_str = amount_match.group(1)
                    try:
                        amount_val = float(amount_str)
                        if amount_val > 100:
                            result['TotalAmount'] = amount_str
                            result['AmountInFiguers'] = amount_str
                            print(f"    ✅ 方法2识别价税合计: {amount_str}")
                    except:
                        continue
            
            # 方法3: 查找包含两个金额的行，取较大的那个（通常是价税合计）
            elif not result['TotalAmount'] and re.findall(r'¥(\d+\.\d{2})', line):
                print(f"  方法3匹配: 包含金额符号的行")
                amounts = re.findall(r'¥(\d+\.\d{2})', line)
                if len(amounts) >= 2:
                    print(f"    找到多个金额: {amounts}")
                    # 取较大的金额作为价税合计
                    amounts_float = [float(amt) for amt in amounts]
                    max_amount = max(amounts_float)
                    if max_amount > 100:
                        result['TotalAmount'] = str(max_amount)
                        result['AmountInFiguers'] = str(max_amount)
                        print(f"    ✅ 方法3识别价税合计: {max_amount} (取最大值)")
                elif len(amounts) == 1:
                    print(f"    找到单个金额: {amounts[0]}")
            
            # 方法4: 备用方法 - 查找任何合理的金额（但优先级最低）
            elif not result['TotalAmount']:
                print(f"  方法4匹配: 备用金额识别")
                amount_patterns = [
                    r'¥(\d+\.?\d*)',
                    r'￥(\d+\.?\d*)',
                    r'(\d+\.\d{2})'
                ]
                
                for pattern in amount_patterns:
                    match = re.search(pattern, line)
                    if match:
                        amount_str = match.group(1)
                        try:
                            amount_val = float(amount_str)
                            # 只有在没有找到更好的金额时才使用
                            if amount_val > 100 and (not result['TotalAmount'] or amount_val > float(result['TotalAmount'])):
                                result['TotalAmount'] = amount_str
                                result['AmountInFiguers'] = amount_str
                                print(f"    ⚠️ 方法4识别金额: {amount_str} (备用)")
                        except:
                            continue
            
            if result['TotalAmount']:
                print(f"  🎯 已识别到金额: {result['TotalAmount']}，继续检查是否有更好的...")
        
        print(f"\n=== 最终结果 ===")
        if result['TotalAmount']:
            print(f"✅ 识别到金额: {result['TotalAmount']}")
        else:
            print(f"❌ 未识别到金额")
        
        print(f"\n期望价税合计: 1203.62 (从图片看到)")
        if result['TotalAmount'] == '1203.62':
            print(f"✅ 识别正确！")
        else:
            print(f"❌ 识别错误！应该是价税合计，不是金额")
        
    except Exception as e:
        print(f"调试失败: {str(e)}")

if __name__ == "__main__":
    debug_amount_recognition()
