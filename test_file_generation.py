#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件生成功能
"""

import os
import sys
import time
from datetime import datetime

def test_file_generation():
    """测试文件生成功能"""
    
    # 模拟的测试数据
    template_path = r"D:\vscode project\fpcl\1\模板.xls"
    export_list_path = r"D:\vscode project\fpcl\1\202501导出清单.xlsx"
    
    # 检查文件是否存在
    if not os.path.exists(template_path):
        print(f"模板文件不存在: {template_path}")
        return
    
    if not os.path.exists(export_list_path):
        print(f"导出清单不存在: {export_list_path}")
        return
    
    print("开始测试文件生成...")
    print(f"模板文件: {template_path}")
    print(f"导出清单: {export_list_path}")
    
    # 模拟生成的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    keywords_str = "锦阳_顺洋_来宁_奥捷_奥源"
    new_template_name = f"{keywords_str}报销表_{timestamp}.xlsx"
    new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)
    
    print(f"预期生成文件: {new_template_path}")
    
    # 检查目标文件夹的权限
    target_dir = os.path.dirname(template_path)
    print(f"目标文件夹: {target_dir}")
    
    if not os.path.exists(target_dir):
        print(f"目标文件夹不存在: {target_dir}")
        return
    
    if not os.access(target_dir, os.W_OK):
        print(f"目标文件夹没有写权限: {target_dir}")
        return
    
    print("目标文件夹权限检查通过")
    
    # 列出目标文件夹中的现有文件
    print("\n目标文件夹中的现有文件:")
    try:
        files = os.listdir(target_dir)
        for file in files:
            if file.endswith('.xlsx') or file.endswith('.xls'):
                file_path = os.path.join(target_dir, file)
                mtime = os.path.getmtime(file_path)
                mtime_str = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
                print(f"  {file} (修改时间: {mtime_str})")
    except Exception as e:
        print(f"列出文件时出错: {str(e)}")
    
    print("\n测试完成")

def check_recent_files():
    """检查最近生成的文件"""
    
    target_dir = r"D:\vscode project\fpcl\1"
    
    if not os.path.exists(target_dir):
        print(f"目标文件夹不存在: {target_dir}")
        return
    
    print(f"检查文件夹: {target_dir}")
    print("最近10分钟内生成的Excel文件:")
    
    current_time = time.time()
    recent_files = []
    
    try:
        for file in os.listdir(target_dir):
            if file.endswith('.xlsx') or file.endswith('.xls'):
                file_path = os.path.join(target_dir, file)
                mtime = os.path.getmtime(file_path)
                
                # 检查是否是最近10分钟内的文件
                if current_time - mtime < 600:  # 600秒 = 10分钟
                    mtime_str = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
                    size = os.path.getsize(file_path)
                    recent_files.append((file, mtime_str, size))
        
        if recent_files:
            recent_files.sort(key=lambda x: x[1], reverse=True)  # 按时间排序
            for file, mtime_str, size in recent_files:
                print(f"  ✅ {file}")
                print(f"     修改时间: {mtime_str}")
                print(f"     文件大小: {size} 字节")
                print()
        else:
            print("  ❌ 没有找到最近生成的文件")
            
    except Exception as e:
        print(f"检查文件时出错: {str(e)}")

def monitor_file_generation():
    """监控文件生成"""
    
    target_dir = r"D:\vscode project\fpcl\1"
    
    if not os.path.exists(target_dir):
        print(f"目标文件夹不存在: {target_dir}")
        return
    
    print(f"开始监控文件夹: {target_dir}")
    print("请运行主程序，我将监控新文件的生成...")
    print("按 Ctrl+C 停止监控")
    
    # 记录初始文件列表
    initial_files = set()
    try:
        for file in os.listdir(target_dir):
            if file.endswith('.xlsx') or file.endswith('.xls'):
                initial_files.add(file)
    except:
        pass
    
    print(f"初始文件数量: {len(initial_files)}")
    
    try:
        while True:
            time.sleep(2)  # 每2秒检查一次
            
            current_files = set()
            try:
                for file in os.listdir(target_dir):
                    if file.endswith('.xlsx') or file.endswith('.xls'):
                        current_files.add(file)
            except:
                continue
            
            # 检查新文件
            new_files = current_files - initial_files
            if new_files:
                for new_file in new_files:
                    file_path = os.path.join(target_dir, new_file)
                    mtime = os.path.getmtime(file_path)
                    mtime_str = datetime.fromtimestamp(mtime).strftime("%Y-%m-%d %H:%M:%S")
                    size = os.path.getsize(file_path)
                    
                    print(f"\n🎉 发现新文件!")
                    print(f"   文件名: {new_file}")
                    print(f"   创建时间: {mtime_str}")
                    print(f"   文件大小: {size} 字节")
                    print(f"   完整路径: {file_path}")
                
                initial_files = current_files
                
    except KeyboardInterrupt:
        print("\n监控已停止")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "check":
            check_recent_files()
        elif sys.argv[1] == "monitor":
            monitor_file_generation()
        else:
            test_file_generation()
    else:
        print("文件生成测试工具")
        print("用法:")
        print("  python test_file_generation.py        - 基础测试")
        print("  python test_file_generation.py check  - 检查最近文件")
        print("  python test_file_generation.py monitor - 监控文件生成")
        print()
        test_file_generation()
