info = {
    "name": "wae",
    "date_order": "<PERSON><PERSON>",
    "january": [
        "jen",
        "jenner"
    ],
    "february": [
        "hor",
        "hornig"
    ],
    "march": [
        "mär",
        "märze"
    ],
    "april": [
        "abr",
        "abrille"
    ],
    "may": [
        "mei",
        "meije"
    ],
    "june": [
        "brá",
        "brá<PERSON>et"
    ],
    "july": [
        "hei",
        "heiwet"
    ],
    "august": [
        "öig",
        "öigšte"
    ],
    "september": [
        "her",
        "herbštmánet"
    ],
    "october": [
        "wím",
        "wímánet"
    ],
    "november": [
        "win",
        "wintermánet"
    ],
    "december": [
        "chr",
        "chrištmánet"
    ],
    "monday": [
        "män",
        "mäntag"
    ],
    "tuesday": [
        "ziš",
        "zištag"
    ],
    "wednesday": [
        "mit",
        "mittwu<PERSON>"
    ],
    "thursday": [
        "fró",
        "fróntag"
    ],
    "friday": [
        "fri",
        "fritag"
    ],
    "saturday": [
        "sam",
        "samštag"
    ],
    "sunday": [
        "sun",
        "sunntag"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "jár"
    ],
    "month": [
        "mánet"
    ],
    "week": [
        "wuča"
    ],
    "day": [
        "tag"
    ],
    "hour": [
        "schtund"
    ],
    "minute": [
        "mínütta"
    ],
    "second": [
        "sekunda"
    ],
    "relative-type": {
        "0 day ago": [
            "hitte"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "gešter"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "móre"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "vor (\\d+[.,]?\\d*) tag",
            "vor (\\d+[.,]?\\d*) täg"
        ],
        "\\1 hour ago": [
            "vor (\\d+[.,]?\\d*) stund",
            "vor (\\d+[.,]?\\d*) stunde"
        ],
        "\\1 minute ago": [
            "vor (\\d+[.,]?\\d*) minüta",
            "vor (\\d+[.,]?\\d*) minüte"
        ],
        "\\1 month ago": [
            "vor (\\d+[.,]?\\d*) mánet"
        ],
        "\\1 second ago": [
            "vor (\\d+[.,]?\\d*) sekund",
            "vor (\\d+[.,]?\\d*) sekunde"
        ],
        "\\1 week ago": [
            "cor (\\d+[.,]?\\d*) wučä",
            "vor (\\d+[.,]?\\d*) wuča"
        ],
        "\\1 year ago": [
            "cor (\\d+[.,]?\\d*) jár",
            "vor (\\d+[.,]?\\d*) jár"
        ],
        "in \\1 day": [
            "i (\\d+[.,]?\\d*) tag",
            "i (\\d+[.,]?\\d*) täg"
        ],
        "in \\1 hour": [
            "i (\\d+[.,]?\\d*) stund",
            "i (\\d+[.,]?\\d*) stunde"
        ],
        "in \\1 minute": [
            "i (\\d+[.,]?\\d*) minüta",
            "i (\\d+[.,]?\\d*) minüte"
        ],
        "in \\1 month": [
            "i (\\d+[.,]?\\d*) mánet"
        ],
        "in \\1 second": [
            "i (\\d+[.,]?\\d*) sekund",
            "i (\\d+[.,]?\\d*) sekunde"
        ],
        "in \\1 week": [
            "i (\\d+[.,]?\\d*) wuča",
            "i (\\d+[.,]?\\d*) wučä"
        ],
        "in \\1 year": [
            "i (\\d+[.,]?\\d*) jár"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
