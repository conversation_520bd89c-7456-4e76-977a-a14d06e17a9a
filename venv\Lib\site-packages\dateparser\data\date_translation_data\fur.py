info = {
    "name": "fur",
    "date_order": "DMY",
    "january": [
        "zen",
        "zenâr"
    ],
    "february": [
        "fev",
        "fevrâr"
    ],
    "march": [
        "mar",
        "març"
    ],
    "april": [
        "avr",
        "avrîl"
    ],
    "may": [
        "mai"
    ],
    "june": [
        "jug",
        "jugn"
    ],
    "july": [
        "lui"
    ],
    "august": [
        "avo",
        "avost"
    ],
    "september": [
        "set",
        "setembar"
    ],
    "october": [
        "otu",
        "otubar"
    ],
    "november": [
        "nov",
        "novembar"
    ],
    "december": [
        "dic",
        "dicembar"
    ],
    "monday": [
        "lun",
        "lunis"
    ],
    "tuesday": [
        "mar",
        "martars"
    ],
    "wednesday": [
        "mie",
        "miercus"
    ],
    "thursday": [
        "joi",
        "joibe"
    ],
    "friday": [
        "vin",
        "vinars"
    ],
    "saturday": [
        "sab",
        "sabide"
    ],
    "sunday": [
        "dom",
        "domenie"
    ],
    "am": [
        "a"
    ],
    "pm": [
        "p"
    ],
    "year": [
        "an"
    ],
    "month": [
        "mês"
    ],
    "week": [
        "setemane"
    ],
    "day": [
        "dì"
    ],
    "hour": [
        "ore"
    ],
    "minute": [
        "minût"
    ],
    "second": [
        "secont"
    ],
    "relative-type": {
        "0 day ago": [
            "vuê"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "îr"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "doman"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) zornade indaûr",
            "(\\d+[.,]?\\d*) zornadis indaûr"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) ore indaûr",
            "(\\d+[.,]?\\d*) oris indaûr"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) minût indaûr",
            "(\\d+[.,]?\\d*) minûts indaûr"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) mês indaûr"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) secont indaûr",
            "(\\d+[.,]?\\d*) seconts indaûr"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) setemane indaûr",
            "(\\d+[.,]?\\d*) setemanis indaûr"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) agns indaûr",
            "(\\d+[.,]?\\d*) an indaûr"
        ],
        "in \\1 day": [
            "ca di (\\d+[.,]?\\d*) zornade",
            "ca di (\\d+[.,]?\\d*) zornadis"
        ],
        "in \\1 hour": [
            "ca di (\\d+[.,]?\\d*) ore",
            "ca di (\\d+[.,]?\\d*) oris"
        ],
        "in \\1 minute": [
            "ca di (\\d+[.,]?\\d*) minût",
            "ca di (\\d+[.,]?\\d*) minûts"
        ],
        "in \\1 month": [
            "ca di (\\d+[.,]?\\d*) mês"
        ],
        "in \\1 second": [
            "ca di (\\d+[.,]?\\d*) secont",
            "ca di (\\d+[.,]?\\d*) seconts"
        ],
        "in \\1 week": [
            "ca di (\\d+[.,]?\\d*) setemane",
            "ca di (\\d+[.,]?\\d*) setemanis"
        ],
        "in \\1 year": [
            "ca di (\\d+[.,]?\\d*) agns",
            "ca di (\\d+[.,]?\\d*) an"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
