info = {
    "name": "ka",
    "date_order": "DMY",
    "january": [
        "იან",
        "იანვარი"
    ],
    "february": [
        "თებ",
        "თებერვალი"
    ],
    "march": [
        "მარ",
        "მარტი"
    ],
    "april": [
        "აპრ",
        "აპრილი"
    ],
    "may": [
        "მაი",
        "მაისი"
    ],
    "june": [
        "ივნ",
        "ივნისი"
    ],
    "july": [
        "ივლ",
        "ივლისი"
    ],
    "august": [
        "აგვ",
        "აგვისტო"
    ],
    "september": [
        "სექ",
        "სექტემბერი"
    ],
    "october": [
        "ოქტ",
        "ოქტომბერი"
    ],
    "november": [
        "ნოე",
        "ნოემბერი"
    ],
    "december": [
        "დეკ",
        "დეკემბერი"
    ],
    "monday": [
        "ორშ",
        "ორშაბათი"
    ],
    "tuesday": [
        "სამ",
        "სამშაბათი"
    ],
    "wednesday": [
        "ოთხ",
        "ოთხშაბათი"
    ],
    "thursday": [
        "ხუთ",
        "ხუთშაბათი"
    ],
    "friday": [
        "პარ",
        "პარასკევი"
    ],
    "saturday": [
        "შაბ",
        "შაბათი"
    ],
    "sunday": [
        "კვი",
        "კვირა"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm",
        "შუადღ შემდეგ"
    ],
    "year": [
        "წ",
        "წელი",
        "წლის"
    ],
    "month": [
        "თვე"
    ],
    "week": [
        "კვ",
        "კვირა"
    ],
    "day": [
        "დღე"
    ],
    "hour": [
        "საათი",
        "სთ"
    ],
    "minute": [
        "წთ",
        "წუთი"
    ],
    "second": [
        "წამი",
        "წმ"
    ],
    "relative-type": {
        "0 day ago": [
            "დღეს"
        ],
        "0 hour ago": [
            "ამ საათში"
        ],
        "0 minute ago": [
            "ამ წუთში"
        ],
        "0 month ago": [
            "ამ თვეში"
        ],
        "0 second ago": [
            "ახლა"
        ],
        "0 week ago": [
            "ამ კვირაში"
        ],
        "0 year ago": [
            "ამ წელს"
        ],
        "1 day ago": [
            "გუშინ"
        ],
        "1 month ago": [
            "გასულ თვეს"
        ],
        "1 week ago": [
            "გასულ კვირაში"
        ],
        "1 year ago": [
            "გასულ წელს"
        ],
        "in 1 day": [
            "ხვალ"
        ],
        "in 1 month": [
            "მომავალ თვეს"
        ],
        "in 1 week": [
            "მომავალ კვირაში"
        ],
        "in 1 year": [
            "მომავალ წელს"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) დღის წინ"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) საათის წინ",
            "(\\d+[.,]?\\d*) სთ წინ"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) წთ წინ",
            "(\\d+[.,]?\\d*) წუთის წინ"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) თვის წინ"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) წამის წინ",
            "(\\d+[.,]?\\d*) წმ წინ"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) კვ წინ",
            "(\\d+[.,]?\\d*) კვირის წინ"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) წლის წინ"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) დღეში"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) საათში"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) წუთში"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) თვეში"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) წამში"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) კვირაში"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) წელიწადში",
            "(\\d+[.,]?\\d*) წელში"
        ]
    },
    "locale_specific": {},
    "skip": [
        "და",
        "დაახლოებით",
        "ზე",
        "ის",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 1,
    "ago": [
        "წინ"
    ],
    "in": [
        "დღეიდან"
    ],
    "simplifications": [
        {
            "ერთ": "1"
        }
    ]
}
