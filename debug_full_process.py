#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试完整处理流程
"""

import os
import sys
import traceback
from invoice2data.input import pdfplumber as pdfplumber_reader

def debug_invoice_recognition():
    """调试发票识别流程"""
    
    # 测试文件夹
    test_folder = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张"
    keywords = ["锦阳", "顺洋", "来宁", "奥捷", "奥源"]
    
    print("开始调试发票识别流程...")
    print(f"测试文件夹: {test_folder}")
    print(f"关键词: {keywords}")
    
    if not os.path.exists(test_folder):
        print(f"❌ 测试文件夹不存在: {test_folder}")
        return
    
    # 列出所有PDF文件
    pdf_files = [f for f in os.listdir(test_folder) if f.lower().endswith('.pdf')]
    print(f"\n找到 {len(pdf_files)} 个PDF文件:")
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"  {i}. {pdf_file}")
    
    if not pdf_files:
        print("❌ 没有找到PDF文件")
        return
    
    # 对每个PDF文件进行识别测试
    pdf_paths_data = {}
    
    for pdf_file in pdf_files:
        pdf_path = os.path.join(test_folder, pdf_file)
        print(f"\n{'='*60}")
        print(f"处理文件: {pdf_file}")
        print(f"完整路径: {pdf_path}")
        
        try:
            # 提取文本
            print("正在提取PDF文本...")
            text_content = pdfplumber_reader.to_text(pdf_path)
            
            if not text_content:
                print("❌ 文本提取失败")
                continue
            
            print(f"✅ 文本提取成功，长度: {len(text_content)} 字符")
            
            # 查找销售方名称
            lines = text_content.split('\n')
            seller_name = ""
            
            for line in lines:
                line = line.strip()
                if '名称：' in line and any(keyword in line for keyword in ['新能源', '科技', '有限公司']):
                    import re
                    name_match = re.search(r'名称：(.+)', line)
                    if name_match:
                        seller_name = name_match.group(1).strip()
                        break
            
            print(f"识别的销售方: {seller_name}")
            
            # 检查哪个关键词匹配
            matched_keywords = []
            for keyword in keywords:
                if keyword in seller_name:
                    matched_keywords.append(keyword)
            
            if matched_keywords:
                print(f"✅ 匹配的关键词: {matched_keywords}")
                # 使用第一个匹配的关键词
                pdf_paths_data[matched_keywords[0]] = (pdf_path, {"SellerName": seller_name})
            else:
                print(f"❌ 没有匹配的关键词")
                print(f"   销售方名称: '{seller_name}'")
                print(f"   查找的关键词: {keywords}")
                
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("识别结果总结:")
    print(f"成功匹配的关键词数量: {len(pdf_paths_data)}")
    
    for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
        print(f"  {keyword}: {os.path.basename(pdf_path)}")
        print(f"    销售方: {invoice_data.get('SellerName', '未知')}")
    
    if not pdf_paths_data:
        print("❌ 没有找到任何匹配的发票")
        print("\n可能的原因:")
        print("1. 关键词不在销售方名称中")
        print("2. 销售方名称识别失败")
        print("3. PDF文本提取有问题")
        
        # 显示第一个PDF的详细文本内容
        if pdf_files:
            first_pdf = os.path.join(test_folder, pdf_files[0])
            print(f"\n显示第一个PDF的文本内容 ({pdf_files[0]}):")
            try:
                text_content = pdfplumber_reader.to_text(first_pdf)
                lines = text_content.split('\n')
                print("前20行内容:")
                for i, line in enumerate(lines[:20], 1):
                    print(f"  {i:2d}: {line}")
                
                print("\n包含'名称'的行:")
                for i, line in enumerate(lines, 1):
                    if '名称' in line:
                        print(f"  {i:2d}: {line}")
                        
            except Exception as e:
                print(f"显示文本内容失败: {str(e)}")
    
    return pdf_paths_data

def debug_excel_processing_with_data(pdf_paths_data):
    """使用实际数据调试Excel处理"""
    
    if not pdf_paths_data:
        print("没有数据可以处理Excel")
        return
    
    print(f"\n开始调试Excel处理，数据量: {len(pdf_paths_data)}")
    
    # 模拟调用process_excel函数
    template_path = r"D:\vscode project\fpcl\1\模板.xls"
    export_list_path = r"D:\vscode project\fpcl\1\202501导出清单.xlsx"
    
    print(f"模板文件: {template_path}")
    print(f"导出清单: {export_list_path}")
    
    # 检查文件是否存在
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    if not os.path.exists(export_list_path):
        print(f"❌ 导出清单不存在: {export_list_path}")
        return
    
    print("✅ 文件存在检查通过")
    print("注意: 这里只是模拟，不会实际调用Excel处理")
    print("如果要实际处理，请运行完整的主程序")

if __name__ == "__main__":
    print("完整流程调试工具")
    print("=" * 60)
    
    # 调试发票识别
    pdf_paths_data = debug_invoice_recognition()
    
    # 调试Excel处理
    debug_excel_processing_with_data(pdf_paths_data)
