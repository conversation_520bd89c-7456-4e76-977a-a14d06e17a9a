#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用真实数据测试Excel处理
"""

import win32com.client
from datetime import datetime
import os
import traceback

def test_excel_processing_with_real_data():
    """使用真实数据测试Excel处理"""
    
    # 模拟真实的发票数据
    pdf_paths_data = {
        "奥捷": (
            r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf",
            {
                'InvoiceTypeOrg': '增值税专用发票',
                'AmountInFiguers': '1065.15',
                'TotalAmount': '1065.15',
                'CommodityTaxRate': ['13%'],
                'TotalTax': '138.47',
                'SellerName': '广东奥捷新能源科技有限公司',
                'Remarks': '购方开户银行:中国工商银行揭阳分行;银行账号:2019002129200505667;销方开户银行:建设银行爵溪支行;银行账号:33150199555600000291;'
            }
        )
    }
    
    template_path = r"D:\vscode project\fpcl\1\模板.xls"
    export_list_path = r"D:\vscode project\fpcl\1\202501导出清单.xlsx"
    
    print("开始测试Excel处理...")
    print(f"模板文件: {template_path}")
    print(f"导出清单: {export_list_path}")
    print(f"测试数据: {list(pdf_paths_data.keys())}")
    
    # 检查文件是否存在
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    if not os.path.exists(export_list_path):
        print(f"❌ 导出清单不存在: {export_list_path}")
        return
    
    print("✅ 文件存在检查通过")
    
    # 生成新文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    keywords_str = "_".join(pdf_paths_data.keys())
    new_template_name = f"{keywords_str}报销表_{timestamp}.xlsx"
    new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)
    
    print(f"新文件路径: {new_template_path}")
    
    excel = None
    wb_template = None
    wb = None
    
    try:
        print("正在启动Excel应用程序...")
        excel = win32com.client.Dispatch('Excel.Application')
        excel.Visible = False
        excel.DisplayAlerts = False
        print("✅ Excel应用程序启动成功")
        
        print("正在打开模板文件...")
        wb_template = excel.Workbooks.Open(template_path)
        print("✅ 模板文件打开成功")
        
        print("正在另存为新文件...")
        wb_template.SaveAs(
            Filename=new_template_path,
            FileFormat=51,  # 51 = xlsx格式
            CreateBackup=False
        )
        print("✅ 新文件创建成功")
        
        wb_template.Close()
        print("✅ 模板文件已关闭")
        
        print("正在重新打开新文件...")
        wb = excel.Workbooks.Open(new_template_path)
        ws = wb.Worksheets(1)
        print("✅ 新文件打开成功")
        
        # 模拟简单的数据填充
        current_row = 5
        
        for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
            print(f"\n处理关键词: {keyword}")
            
            # 读取导出清单
            print("正在读取导出清单...")
            export_wb = excel.Workbooks.Open(export_list_path)
            export_ws = export_wb.Worksheets(1)
            
            # 获取数据范围
            used_range = export_ws.UsedRange
            nrows = used_range.Rows.Count
            print(f"导出清单总行数: {nrows}")
            
            # 查找匹配的行
            valid_rows = []
            for row in range(2, nrows + 1):
                cell_value = export_ws.Cells(row, 2).Value
                amount_value = export_ws.Cells(row, 9).Value
                
                if (cell_value and keyword in str(cell_value) and
                    amount_value and float(amount_value or 0) != 0):
                    valid_rows.append(row)
                    print(f"找到匹配行: 第{row}行, 内容: {cell_value}, 金额: {amount_value}")
            
            print(f"找到{len(valid_rows)}个匹配行")
            
            if len(valid_rows) > 0:
                # 插入行
                ws.Range(f"{current_row}:{current_row+len(valid_rows)-1}").EntireRow.Insert()
                
                # 填充一些测试数据
                for i, source_row in enumerate(valid_rows):
                    dest_row = current_row + i
                    ws.Cells(dest_row, 2).Value = i + 1  # 序号
                    ws.Cells(dest_row, 5).Value = export_ws.Cells(source_row, 2).Value  # B -> E
                    
                # 填充发票数据
                if invoice_data:
                    ws.Cells(current_row, 10).Value = invoice_data.get('InvoiceTypeOrg', '')  # J列
                    ws.Cells(current_row, 11).Value = invoice_data.get('AmountInFiguers', '')  # K列
                    ws.Cells(current_row, 18).Value = invoice_data.get('SellerName', '')  # R列
                
                current_row += len(valid_rows)
            
            export_wb.Close(False)
            print(f"✅ 关键词 {keyword} 处理完成")
        
        print("\n正在保存文件...")
        wb.Save()
        print("✅ 文件保存成功")
        
        # 检查文件是否存在
        if os.path.exists(new_template_path):
            file_size = os.path.getsize(new_template_path)
            print(f"✅ 文件确实存在，大小: {file_size} 字节")
            print(f"✅ 完整路径: {new_template_path}")
        else:
            print("❌ 文件保存后不存在")
        
    except Exception as e:
        print(f"❌ Excel处理失败: {str(e)}")
        print("详细错误信息:")
        traceback.print_exc()
        
    finally:
        try:
            if wb:
                print("正在关闭工作簿...")
                wb.Close()
                print("✅ 工作簿已关闭")
        except Exception as e:
            print(f"关闭工作簿时出错: {str(e)}")
        
        try:
            if excel:
                print("正在退出Excel应用程序...")
                excel.Quit()
                print("✅ Excel应用程序已退出")
        except Exception as e:
            print(f"退出Excel时出错: {str(e)}")

if __name__ == "__main__":
    print("Excel处理真实数据测试")
    print("=" * 50)
    test_excel_processing_with_real_data()
