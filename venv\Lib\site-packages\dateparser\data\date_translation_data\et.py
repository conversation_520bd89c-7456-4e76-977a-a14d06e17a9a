info = {
    "name": "et",
    "date_order": "DMY",
    "january": [
        "jaan",
        "jaanuar"
    ],
    "february": [
        "veebr",
        "veebruar"
    ],
    "march": [
        "märts"
    ],
    "april": [
        "apr",
        "aprill"
    ],
    "may": [
        "mai"
    ],
    "june": [
        "juuni"
    ],
    "july": [
        "juuli"
    ],
    "august": [
        "aug",
        "august"
    ],
    "september": [
        "sept",
        "september"
    ],
    "october": [
        "okt",
        "oktoober"
    ],
    "november": [
        "nov",
        "november"
    ],
    "december": [
        "dets",
        "detsember"
    ],
    "monday": [
        "e",
        "esmaspäev"
    ],
    "tuesday": [
        "t",
        "teisipäev"
    ],
    "wednesday": [
        "k",
        "kolmapäev"
    ],
    "thursday": [
        "n",
        "nelja<PERSON><PERSON><PERSON>"
    ],
    "friday": [
        "r",
        "reede"
    ],
    "saturday": [
        "l",
        "laupäev"
    ],
    "sunday": [
        "p",
        "pühapäev"
    ],
    "am": [
        "am"
    ],
    "pm": [
        "pm"
    ],
    "year": [
        "a",
        "aasta"
    ],
    "month": [
        "k",
        "kuu"
    ],
    "week": [
        "näd",
        "nädal"
    ],
    "day": [
        "p",
        "päev"
    ],
    "hour": [
        "t",
        "tund"
    ],
    "minute": [
        "min",
        "minut"
    ],
    "second": [
        "s",
        "sek",
        "sekund"
    ],
    "relative-type": {
        "0 day ago": [
            "täna"
        ],
        "0 hour ago": [
            "praegusel tunnil"
        ],
        "0 minute ago": [
            "praegusel minutil"
        ],
        "0 month ago": [
            "käesolev kuu"
        ],
        "0 second ago": [
            "nüüd"
        ],
        "0 week ago": [
            "käesolev nädal"
        ],
        "0 year ago": [
            "käesolev aasta"
        ],
        "1 day ago": [
            "eile"
        ],
        "1 month ago": [
            "eelmine kuu"
        ],
        "1 week ago": [
            "eelmine nädal"
        ],
        "1 year ago": [
            "eelmine aasta"
        ],
        "in 1 day": [
            "homme"
        ],
        "in 1 month": [
            "järgmine kuu"
        ],
        "in 1 week": [
            "järgmine nädal"
        ],
        "in 1 year": [
            "järgmine aasta"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) p eest",
            "(\\d+[.,]?\\d*) päeva eest"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) t eest",
            "(\\d+[.,]?\\d*) tunni eest"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) min eest",
            "(\\d+[.,]?\\d*) minuti eest"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) k eest",
            "(\\d+[.,]?\\d*) kuu eest"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) s eest",
            "(\\d+[.,]?\\d*) sek eest",
            "(\\d+[.,]?\\d*) sekundi eest"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) näd eest",
            "(\\d+[.,]?\\d*) nädala eest"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) a eest",
            "(\\d+[.,]?\\d*) aasta eest"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) p pärast",
            "(\\d+[.,]?\\d*) päeva pärast"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) t pärast",
            "(\\d+[.,]?\\d*) tunni pärast"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) min pärast",
            "(\\d+[.,]?\\d*) minuti pärast"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) k pärast",
            "(\\d+[.,]?\\d*) kuu pärast"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) s pärast",
            "(\\d+[.,]?\\d*) sek pärast",
            "(\\d+[.,]?\\d*) sekundi pärast"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) näd pärast",
            "(\\d+[.,]?\\d*) nädala pärast"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) a pärast",
            "(\\d+[.,]?\\d*) aasta pärast"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
