#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定发票文件的识别
"""

import os
import sys
import traceback
import gc
from paddleocr import PaddleOCR
import fitz  # PyMuPDF

def test_specific_invoice():
    """测试特定的发票文件"""
    
    # 发票文件路径
    invoice_path = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf"
    
    if not os.path.exists(invoice_path):
        print(f"发票文件不存在: {invoice_path}")
        return
    
    print(f"测试发票文件: {os.path.basename(invoice_path)}")
    
    try:
        # 1. PDF转换
        print("步骤1: 转换PDF为图片...")
        doc = fitz.open(invoice_path)
        page = doc[0]
        mat = fitz.Matrix(2.0, 2.0)  # 2倍缩放
        pix = page.get_pixmap(matrix=mat)
        
        temp_image = "test_specific_invoice.png"
        pix.save(temp_image)
        doc.close()
        
        print(f"✓ PDF转换成功: {temp_image}")
        print(f"图片大小: {os.path.getsize(temp_image) / (1024**2):.2f} MB")
        
        # 2. 初始化OCR
        print("步骤2: 初始化PaddleOCR...")
        ocr = PaddleOCR(lang='ch')
        print("✓ OCR初始化成功")
        
        # 3. OCR识别
        print("步骤3: 开始OCR识别...")
        print("注意：这一步可能需要较长时间，请耐心等待...")
        
        try:
            result = ocr.predict(temp_image)
            print("✓ OCR识别完成")
            
            if result and len(result) > 0:
                page_result = result[0]
                texts = page_result.get('rec_texts', [])
                scores = page_result.get('rec_scores', [])
                
                print(f"\n识别到 {len(texts)} 个文本区域:")
                
                # 查找关键信息
                key_info = {}
                
                for i, (text, score) in enumerate(zip(texts, scores)):
                    text_str = str(text).strip()
                    
                    # 只显示前20个结果
                    if i < 20:
                        print(f"{i+1:2d}. {text_str} (置信度: {score:.3f})")
                    
                    # 提取关键信息
                    if '增值税专用发票' in text_str:
                        key_info['发票类型'] = '增值税专用发票'
                    elif '增值税普通发票' in text_str:
                        key_info['发票类型'] = '增值税普通发票'
                    
                    if '奥捷' in text_str or '广东奥捷' in text_str:
                        key_info['销售方'] = text_str
                    
                    if '¥' in text_str and any(c.isdigit() for c in text_str):
                        if '价税合计' not in key_info:
                            key_info['价税合计'] = text_str
                    
                    if '%' in text_str and any(c.isdigit() for c in text_str):
                        if '税率' not in key_info:
                            key_info['税率'] = text_str
                
                print(f"\n=== 提取的关键信息 ===")
                for key, value in key_info.items():
                    print(f"{key}: {value}")
                
                print("\n✓ 测试成功完成！")
            else:
                print("✗ 未识别到任何文本")
                
        except Exception as ocr_error:
            print(f"✗ OCR识别失败: {str(ocr_error)}")
            traceback.print_exc()
        
        # 清理资源
        del ocr
        gc.collect()
        
        # 清理临时文件
        if os.path.exists(temp_image):
            os.remove(temp_image)
            print(f"清理临时文件: {temp_image}")
            
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        traceback.print_exc()

if __name__ == "__main__":
    test_specific_invoice()
