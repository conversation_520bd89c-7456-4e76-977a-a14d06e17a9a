info = {
    "name": "hi",
    "date_order": "DM<PERSON>",
    "january": [
        "जनवरी",
        "जन॰"
    ],
    "february": [
        "फ़रवरी",
        "फ़र॰"
    ],
    "march": [
        "मार्च"
    ],
    "april": [
        "अप्रैल"
    ],
    "may": [
        "मई"
    ],
    "june": [
        "जून"
    ],
    "july": [
        "जुलाई",
        "जुल॰"
    ],
    "august": [
        "अगस्त",
        "अग॰"
    ],
    "september": [
        "सितंबर",
        "सित॰",
        "सितम्बर"
    ],
    "october": [
        "अक्तूबर",
        "अक्तू॰",
        "अक्टूबर"
    ],
    "november": [
        "नवंबर",
        "नव॰",
        "नवम्बर"
    ],
    "december": [
        "दिसंबर",
        "दिस॰",
        "दिसम्बर"
    ],
    "monday": [
        "सोम",
        "सोमवार"
    ],
    "tuesday": [
        "मंगल",
        "मंगलवार"
    ],
    "wednesday": [
        "बुध",
        "बुधवार"
    ],
    "thursday": [
        "गुरु",
        "गुरुवार"
    ],
    "friday": [
        "शुक्र",
        "शुक्रवार"
    ],
    "saturday": [
        "शनि",
        "शनिवार"
    ],
    "sunday": [
        "रवि",
        "रविवार"
    ],
    "am": [
        "पूर्वाह्न"
    ],
    "pm": [
        "अपराह्न"
    ],
    "year": [
        "वर्ष",
        "साल",
        "वर्षों"
    ],
    "month": [
        "माह",
        "महीना",
        "मास",
        "महीने"
    ],
    "week": [
        "सप्ताह"
    ],
    "day": [
        "दिन",
        "दिवस"
    ],
    "hour": [
        "घं",
        "घंटा",
        "घंटे"
    ],
    "minute": [
        "मि",
        "मिनट"
    ],
    "second": [
        "से",
        "सेकंड"
    ],
    "relative-type": {
        "0 day ago": [
            "आज"
        ],
        "0 hour ago": [
            "यह घंटा"
        ],
        "0 minute ago": [
            "यह मिनट"
        ],
        "0 month ago": [
            "इस माह"
        ],
        "0 second ago": [
            "अब"
        ],
        "0 week ago": [
            "इस सप्ताह"
        ],
        "0 year ago": [
            "इस वर्ष"
        ],
        "1 day ago": [
            "कल"
        ],
        "1 month ago": [
            "पिछला माह"
        ],
        "1 week ago": [
            "पिछला सप्ताह"
        ],
        "1 year ago": [
            "पिछला वर्ष"
        ],
        "in 1 day": [
            "कल"
        ],
        "in 1 month": [
            "अगला माह"
        ],
        "in 1 week": [
            "अगला सप्ताह"
        ],
        "in 1 year": [
            "अगला वर्ष"
        ],
        "2 day ago": [
            "परसों"
        ],
        "1 decade ago": [
            "पिछला दशक"
        ],
        "in 1 decade": [
            "अगला दशक"
        ]
    },
    "relative-type-regex": {
        "\\1 day ago": [
            "(\\d+[.,]?\\d*) दिन पहले"
        ],
        "\\1 hour ago": [
            "(\\d+[.,]?\\d*) घं पहले",
            "(\\d+[.,]?\\d*) घंटे पहले"
        ],
        "\\1 minute ago": [
            "(\\d+[.,]?\\d*) मि पहले",
            "(\\d+[.,]?\\d*) मिनट पहले"
        ],
        "\\1 month ago": [
            "(\\d+[.,]?\\d*) माह पहले"
        ],
        "\\1 second ago": [
            "(\\d+[.,]?\\d*) से पहले",
            "(\\d+[.,]?\\d*) सेकंड पहले"
        ],
        "\\1 week ago": [
            "(\\d+[.,]?\\d*) सप्ताह पहले"
        ],
        "\\1 year ago": [
            "(\\d+[.,]?\\d*) वर्ष पहले"
        ],
        "in \\1 day": [
            "(\\d+[.,]?\\d*) दिन में"
        ],
        "in \\1 hour": [
            "(\\d+[.,]?\\d*) घं में",
            "(\\d+[.,]?\\d*) घंटे में"
        ],
        "in \\1 minute": [
            "(\\d+[.,]?\\d*) मि में",
            "(\\d+[.,]?\\d*) मिनट में"
        ],
        "in \\1 month": [
            "(\\d+[.,]?\\d*) माह में"
        ],
        "in \\1 second": [
            "(\\d+[.,]?\\d*) से में",
            "(\\d+[.,]?\\d*) सेकंड में"
        ],
        "in \\1 week": [
            "(\\d+[.,]?\\d*) सप्ताह में"
        ],
        "in \\1 year": [
            "(\\d+[.,]?\\d*) वर्ष में"
        ],
        "in \\1 decade": [
            "(\\d+[.,]?\\d*) दशक में"
        ],
        "\\1 decade ago": [
            "(\\d+[.,]?\\d*) दशक पहले"
        ]
    },
    "locale_specific": {},
    "skip": [
        "के",
        "को",
        "बजे",
        "सन्",
        "से",
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ],
    "sentence_splitter_group": 3,
    "decade": [
        "दशक"
    ],
    "ago": [
        "पहले",
        "पूर्व"
    ],
    "in": [
        "में",
        "बाद"
    ]
}
