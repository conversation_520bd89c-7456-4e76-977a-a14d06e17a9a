# 发票处理程序 - PaddleOCR版本

## 概述

这是一个改进版的发票处理程序，使用PaddleOCR替代了原来的百度API，实现了完全本地化的发票识别功能。程序可以识别增值税专用发票和普通发票，提取关键信息并自动填入Excel表格。

## 主要改进

1. **本地化识别**: 使用PaddleOCR替代百度API，无需网络连接即可运行
2. **更好的识别效果**: PaddleOCR针对中文文档进行了深度优化
3. **虚拟环境**: 项目运行在独立的Python虚拟环境中，避免依赖冲突
4. **图像预处理**: 增加了图像增强功能，提高OCR识别率

## 系统要求

- Windows 10/11
- Python 3.9+
- 至少4GB内存
- 2GB可用磁盘空间（用于模型文件）

## 安装和配置

### 1. 虚拟环境已配置完成

项目已经配置好虚拟环境，位于 `venv` 文件夹中，包含以下主要依赖：

- PaddlePaddle 3.0.0+
- PaddleOCR 3.0.2+
- OpenCV-Python
- PyMuPDF
- PyWin32

### 2. 启动程序

有两种方式启动程序：

#### 方式一：使用批处理文件（推荐）
双击 `启动发票识别程序.bat` 文件

#### 方式二：手动启动
```bash
# 激活虚拟环境
venv\Scripts\activate

# 运行程序
python "fppl-21.改进版PaddleOCR识别.py"
```

## 使用说明

### 1. 程序界面

程序启动后会显示一个图形界面，包含以下配置项：

- **模板文件**: Excel模板文件路径
- **导出清单**: 数据源Excel文件路径  
- **关键词**: 用于匹配发票的关键词，用"-"分隔
- **发票文件夹**: 包含PDF发票文件的文件夹路径

### 2. 操作步骤

1. **配置文件路径**: 选择模板文件和导出清单文件
2. **设置关键词**: 输入要匹配的公司名称关键词
3. **选择发票文件夹**: 选择包含PDF发票的文件夹
4. **开始处理**: 点击"开始处理"按钮

### 3. 处理流程

程序会自动执行以下步骤：

1. 遍历发票文件夹中的PDF文件
2. 使用PaddleOCR识别发票内容
3. 提取关键信息（发票类型、金额、税率、销售方等）
4. 根据关键词匹配对应的发票
5. 从导出清单中查找匹配的数据行
6. 将数据填入Excel模板并生成新文件

## 识别的发票信息

程序可以识别以下发票信息：

- **发票类型**: 增值税专用发票/增值税普通发票
- **价税合计**: 发票总金额
- **税额**: 税费金额
- **税率**: 增值税税率
- **销售方名称**: 开票公司名称
- **备注信息**: 开户行、账号等

## 文件说明

- `fppl-21.改进版PaddleOCR识别.py`: 主程序文件
- `fppl-21.尝试替代百度识别.py`: 原始版本（使用EasyOCR）
- `requirements.txt`: Python依赖包列表
- `test_paddleocr.py`: PaddleOCR测试脚本
- `启动发票识别程序.bat`: 程序启动脚本
- `venv/`: Python虚拟环境文件夹

## 注意事项

1. **首次运行**: 第一次运行时会自动下载PaddleOCR模型文件（约1-2GB），请确保网络连接正常
2. **文件格式**: 目前支持PDF格式的发票文件
3. **图像质量**: 发票图像质量越高，识别效果越好
4. **关键词匹配**: 确保关键词能准确匹配发票中的销售方名称

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查虚拟环境是否正确激活
   - 确认所有依赖包已正确安装

2. **识别效果不佳**
   - 检查PDF文件是否清晰
   - 尝试调整图像预处理参数

3. **内存不足**
   - 关闭其他占用内存的程序
   - 考虑处理较少的文件

### 日志查看

程序运行时会在界面下方显示详细的处理日志，可以通过日志信息诊断问题。

## 技术支持

如有问题，请检查：
1. 虚拟环境是否正确配置
2. 依赖包是否完整安装
3. 文件路径是否正确
4. 发票文件是否符合要求

## 更新日志

### v2.0 (PaddleOCR版本)
- 使用PaddleOCR替代百度API
- 实现完全本地化识别
- 改进图像预处理算法
- 优化发票信息提取逻辑
- 增加虚拟环境支持
