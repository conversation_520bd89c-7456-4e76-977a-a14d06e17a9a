import win32com.client
from datetime import datetime
import os
import tkinter as tk
from tkinter import filedialog, ttk
from tkinter import messagebox
import re
import cv2
import numpy as np
import easyocr
import fitz  # PyMuPDF
import gc
import traceback

# 全局OCR对象，延迟初始化
ocr_reader = None

def get_ocr_instance():
    """获取EasyOCR实例，延迟初始化"""
    global ocr_reader
    if ocr_reader is None:
        print("正在初始化EasyOCR...")
        try:
            # 初始化EasyOCR，支持中英文
            ocr_reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            print("EasyOCR初始化完成！")
        except Exception as e:
            print(f"EasyOCR初始化失败: {str(e)}")
            raise
    return ocr_reader

def extract_invoice_info_easyocr(ocr_result):
    """从EasyOCR结果中提取发票信息
    EasyOCR返回格式: [([[x1,y1],[x2,y2],[x3,y3],[x4,y4]], text, confidence)]
    """
    result = {
        'InvoiceTypeOrg': '',
        'AmountInFiguers': '',
        'TotalAmount': '',
        'CommodityTaxRate': '',
        'TotalTax': '',
        'SellerName': '',
        'Remarks': ''
    }

    if not ocr_result or len(ocr_result) == 0:
        return result
    
    try:
        # 将所有识别的文本保存，按y坐标排序（从上到下）
        all_text = []
        for item in ocr_result:
            bbox, text, confidence = item
            # 计算文本框的中心y坐标用于排序
            center_y = (bbox[0][1] + bbox[2][1]) / 2
            all_text.append((text.strip(), confidence, center_y, bbox))
        
        # 按y坐标排序，确保按从上到下的顺序处理
        all_text.sort(key=lambda x: x[2])

        print(f"开始提取发票信息...")
        print(f"识别到 {len(all_text)} 个文本区域")

        # 遍历所有文本进行信息提取
        for i, (line, confidence, center_y, bbox) in enumerate(all_text):
            line = str(line).strip()
            print(f"处理文本: {line} (置信度: {confidence:.3f})")

            # 发票类型识别 - 改进逻辑以处理OCR识别错误
            if not result['InvoiceTypeOrg']:
                # 处理OCR可能的识别错误
                line_clean = line.replace('僧', '增').replace('用发票)', '专用发票')
                if '增值税专用发票' in line_clean or '专用发票' in line_clean:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                    print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
                elif '增值税普通发票' in line_clean or '普通发票' in line_clean:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                    print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
                elif '电子发票' in line:
                    # 如果只识别到电子发票，默认为专用发票
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                    print(f"识别发票类型(电子发票): {line} -> {result['InvoiceTypeOrg']}")

            # 价税合计识别 - 改进逻辑
            if not result['TotalAmount']:
                # 处理OCR可能的识别错误，如 芏 -> ¥, 羌 -> ¥
                line_clean = line.replace('芏', '¥').replace('羌', '¥')
                if any(keyword in line_clean for keyword in ['价税合计', '合计金额', '总计', '￥', '¥', '小写']):
                    amount_patterns = [
                        r'￥(\d+\.?\d*)',
                        r'¥(\d+\.?\d*)',
                        r'(\d+\.\d{2})',
                        r'合计.*?(\d+\.\d{2})',
                        r'总.*?(\d+\.\d{2})',
                        r'小写.*?(\d+\.\d{2})'  # 添加小写金额模式
                    ]
                    for pattern in amount_patterns:
                        match = re.search(pattern, line_clean)
                        if match:
                            amount_str = match.group(1)
                            try:
                                amount_val = float(amount_str)
                                if amount_val > 0:
                                    result['TotalAmount'] = amount_str
                                    result['AmountInFiguers'] = amount_str
                                    print(f"识别价税合计: {line} -> {amount_str}")
                                    break
                            except:
                                continue

                # 如果还没找到，尝试直接从数字中找较大的金额
                if not result['TotalAmount']:
                    number_match = re.search(r'(\d{3,}\.\d{2})', line)
                    if number_match:
                        amount_str = number_match.group(1)
                        try:
                            amount_val = float(amount_str)
                            if amount_val > 100:  # 金额应该大于100
                                result['TotalAmount'] = amount_str
                                result['AmountInFiguers'] = amount_str
                                print(f"识别金额(数字): {line} -> {amount_str}")
                        except:
                            pass

            # 税额识别
            if not result['TotalTax']:
                if '税额' in line or ('税' in line and re.search(r'\d+\.\d{2}', line)):
                    tax_patterns = [
                        r'税额.*?(\d+\.\d{2})',
                        r'税.*?(\d+\.\d{2})',
                        r'(\d+\.\d{2})'
                    ]
                    for pattern in tax_patterns:
                        match = re.search(pattern, line)
                        if match:
                            tax_str = match.group(1)
                            try:
                                tax_amount = float(tax_str)
                                if result['TotalAmount']:
                                    total_amount = float(result['TotalAmount'])
                                    if 0 < tax_amount < total_amount:
                                        result['TotalTax'] = tax_str
                                        print(f"识别税额: {line} -> {tax_str}")
                                        break
                                elif 0 < tax_amount < 100000:
                                    result['TotalTax'] = tax_str
                                    print(f"识别税额: {line} -> {tax_str}")
                                    break
                            except:
                                continue

            # 税率识别 - 改进逻辑
            if not result['CommodityTaxRate']:
                if '税率' in line or re.search(r'\d+%', line) or line.strip() in ['13%', '135', '13']:
                    rate_patterns = [
                        r'(\d+(?:\.\d+)?)%',
                        r'税率.*?(\d+(?:\.\d+)?)%',
                        r'^(\d+)$'  # 单独的数字，可能是税率
                    ]
                    for pattern in rate_patterns:
                        match = re.search(pattern, line)
                        if match:
                            rate_str = match.group(1)
                            try:
                                rate_val = float(rate_str)
                                # 处理常见的OCR错误：135 -> 13%
                                if rate_val == 135:
                                    rate_val = 13
                                    rate_str = '13'
                                if 0 <= rate_val <= 20:
                                    result['CommodityTaxRate'] = [rate_str + '%']
                                    print(f"识别税率: {line} -> {rate_str}%")
                                    break
                            except:
                                continue

            # 销售方名称识别
            if not result['SellerName']:
                if '销售方' in line and '名称' in line:
                    seller_patterns = [
                        r'名称[:：]\s*(.+)',
                        r'销售方.*?名称.*?[:：]\s*(.+)'
                    ]
                    for pattern in seller_patterns:
                        match = re.search(pattern, line)
                        if match:
                            seller_name = match.group(1).strip()
                            if len(seller_name) > 3:
                                result['SellerName'] = seller_name
                                print(f"识别销售方: {line} -> {seller_name}")
                                break
                    
                    # 如果当前行没找到，查找下一行
                    if not result['SellerName'] and i + 1 < len(all_text):
                        next_line = all_text[i + 1][0]
                        if '公司' in next_line or '有限' in next_line:
                            result['SellerName'] = next_line.strip()
                            print(f"识别销售方(下一行): {next_line}")

            # 如果还没找到销售方，查找包含公司名称的行
            if not result['SellerName'] and ('公司' in line or '有限' in line) and len(line) > 5:
                if '购' not in line and '买方' not in line:
                    if any(keyword in line for keyword in ['科技', '新能源', '实业', '贸易', '工程', '建设', '电力']):
                        result['SellerName'] = line.strip()
                        print(f"识别销售方(公司名): {line}")

            # 备注信息（开户行和账号）
            if '开户行' in line or '账号' in line or '银行' in line:
                result['Remarks'] = result['Remarks'] + line + ';'

        print(f"信息提取完成: {result}")
        return result
        
    except Exception as e:
        print(f"提取发票信息时出错: {str(e)}")
        traceback.print_exc()
        return result

def pdf_to_image_pymupdf(pdf_path):
    """使用PyMuPDF将PDF转换为图片"""
    try:
        doc = fitz.open(pdf_path)
        page = doc[0]

        # 设置适中的缩放比例
        mat = fitz.Matrix(2.0, 2.0)
        pix = page.get_pixmap(matrix=mat)

        temp_image_path = "temp_invoice_easyocr.png"
        pix.save(temp_image_path)
        doc.close()

        print(f"PyMuPDF转换完成: {temp_image_path}")
        return temp_image_path
    except Exception as e:
        print(f"PyMuPDF转换失败: {str(e)}")
        return None

def recognize_invoice_easyocr(file_path):
    """使用EasyOCR识别发票信息"""
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return None

    print(f"开始处理发票: {file_path}")

    temp_image_path = None
    try:
        # 获取OCR实例
        ocr_instance = get_ocr_instance()
        
        # 如果是PDF，转换为图片
        if file_path.lower().endswith('.pdf'):
            print("正在转换PDF为图片...")
            temp_image_path = pdf_to_image_pymupdf(file_path)
            
            if not temp_image_path:
                print("PDF转换失败")
                return None

            file_path = temp_image_path
            print("PDF转换完成")

        # 使用EasyOCR识别图片
        print("正在进行OCR识别...")
        print(f"使用图片文件: {file_path}")
        
        try:
            result = ocr_instance.readtext(file_path)
            print("OCR识别完成")
        except Exception as ocr_error:
            print(f"OCR识别过程中出错: {str(ocr_error)}")
            traceback.print_exc()
            return None

        if not result or len(result) == 0:
            print("OCR识别结果为空")
            return None

        print(f"OCR识别到 {len(result)} 个文本区域")

        # 从OCR结果中提取发票信息
        invoice_data = extract_invoice_info_easyocr(result)
        print(f"识别结果: {invoice_data}")
        return invoice_data

    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        traceback.print_exc()
        return None
    finally:
        # 清理临时文件
        if temp_image_path and os.path.exists(temp_image_path):
            try:
                os.remove(temp_image_path)
                print("临时文件已清理")
            except:
                pass
        
        # 强制垃圾回收
        gc.collect()

class InvoiceProcessWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("发票处理配置 - EasyOCR版本")
        self.root.geometry("800x600")

        # 文件路径变量
        self.template_path = tk.StringVar(value=r"D:\vscode project\fpcl\1\模板.xls")
        self.export_list_path = tk.StringVar(value=r"D:\vscode project\fpcl\1\202501导出清单.xlsx")
        self.invoice_folder = tk.StringVar()
        # 关键词变量
        self.keyword = tk.StringVar(value="锦阳-顺洋-来宁-奥捷-奥源")

        # 添加日志文本框
        self.log_text = None

        self.create_widgets()

    def create_widgets(self):
        # 模板文件选择
        template_frame = ttk.Frame(self.root, padding="10")
        template_frame.pack(fill="x")
        ttk.Label(template_frame, text="模板文件:").pack(side="left")
        ttk.Entry(template_frame, textvariable=self.template_path, width=60).pack(side="left", padx=5)
        ttk.Button(template_frame, text="浏览",
                  command=lambda: self.browse_file(self.template_path, [("Excel files", "*.xls;*.xlsx")])).pack(side="left")

        # 导出清单文件选择
        export_frame = ttk.Frame(self.root, padding="10")
        export_frame.pack(fill="x")
        ttk.Label(export_frame, text="导出清单:").pack(side="left")
        ttk.Entry(export_frame, textvariable=self.export_list_path, width=60).pack(side="left", padx=5)
        ttk.Button(export_frame, text="浏览",
                  command=lambda: self.browse_file(self.export_list_path, [("Excel files", "*.xls;*.xlsx")])).pack(side="left")

        # 关键词输入
        keyword_frame = ttk.Frame(self.root, padding="10")
        keyword_frame.pack(fill="x")
        ttk.Label(keyword_frame, text="关键词(用-分隔):").pack(side="left")
        ttk.Entry(keyword_frame, textvariable=self.keyword, width=60).pack(side="left", padx=5)

        # 发票文件夹选择
        folder_frame = ttk.Frame(self.root, padding="10")
        folder_frame.pack(fill="x")
        ttk.Label(folder_frame, text="发票文件夹:").pack(side="left")
        ttk.Entry(folder_frame, textvariable=self.invoice_folder, width=60).pack(side="left", padx=5)
        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).pack(side="left")

        # 处理按钮
        ttk.Button(self.root, text="开始处理", command=self.start_process).pack(pady=10)

        # 添加日志窗口
        log_frame = ttk.LabelFrame(self.root, text="处理日志", padding="10")
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        # 放置文本框和滚动条
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 设置文本框只读
        self.log_text.configure(state='disabled')

    def browse_file(self, string_var, file_types):
        filename = filedialog.askopenfilename(filetypes=file_types)
        if filename:
            string_var.set(filename)

    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.invoice_folder.set(folder)

    def find_matching_invoice_for_keyword(self, keyword):
        folder_path = self.invoice_folder.get()

        if not folder_path or not keyword:
            messagebox.showerror("错误", "请选择发票文件夹并输入关键词")
            return None, None

        # 遍历文件夹中的所有PDF文件
        for file in os.listdir(folder_path):
            if file.lower().endswith('.pdf'):
                pdf_path = os.path.join(folder_path, file)
                try:
                    # 使用EasyOCR识别发票
                    invoice_data = recognize_invoice_easyocr(pdf_path)
                    if invoice_data:
                        # 获取销售方名称
                        seller_name = invoice_data.get('SellerName', '')
                        # 检查关键词是否在销售方名称中
                        if keyword in seller_name:
                            print(f"找到匹配发票: {file}")
                            return pdf_path, invoice_data
                except Exception as e:
                    print(f"处理发票 {file} 时出错: {str(e)}")
                    continue

        return None, None

    def log_message(self, message):
        """向日志窗口添加消息"""
        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)  # 滚动到最新内容
        self.log_text.configure(state='disabled')
        # 更新GUI
        self.root.update()

    def start_process(self):
        try:
            # 清空日志窗口
            self.log_text.configure(state='normal')
            self.log_text.delete(1.0, tk.END)
            self.log_text.configure(state='disabled')

            # 验证文件路径
            if not all(os.path.exists(path) for path in [self.template_path.get(), self.export_list_path.get()]):
                self.log_message("错误: 请确保模板文件和导出清单文件路径正确")
                messagebox.showerror("错误", "请确保模板文件和导出清单文件路径正确")
                return

            # 获取所有关键词
            keywords = [k.strip() for k in self.keyword.get().split('-')]
            self.log_message(f"开始处理关键词: {', '.join(keywords)}")

            pdf_paths_data = {}

            # 先收集所有关键词对应的发票数据
            for keyword in keywords:
                self.log_message(f"\n查找关键词: {keyword}")
                matching_pdf, invoice_data = self.find_matching_invoice_for_keyword(keyword)
                if matching_pdf:
                    self.log_message(f"找到匹配发票: {os.path.basename(matching_pdf)}")
                    pdf_paths_data[keyword] = (matching_pdf, invoice_data)
                else:
                    self.log_message(f"未找到关键词 '{keyword}' 匹配的发票")

            if pdf_paths_data:
                self.log_message("\n开始处理Excel文件...")
                self.log_message("注意：Excel处理功能需要完整的process_excel函数")
                self.log_message("当前版本仅演示发票识别功能")
                messagebox.showinfo("成功", f"成功识别 {len(pdf_paths_data)} 个关键词的发票")
            else:
                self.log_message("\n错误: 没有找到任何匹配的发票")
                messagebox.showerror("错误", "没有找到任何匹配的发票")

        except Exception as e:
            error_message = f"处理过程中出错：{str(e)}"
            self.log_message(f"\n{error_message}")
            messagebox.showerror("错误", error_message)

    def run(self):
        self.root.mainloop()

# 简化的测试函数
def test_invoice_recognition():
    """测试发票识别功能"""
    test_file = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf"

    if os.path.exists(test_file):
        print("开始测试EasyOCR发票识别...")
        result = recognize_invoice_easyocr(test_file)

        if result:
            print("\n=== 测试结果 ===")
            for key, value in result.items():
                print(f"{key}: {value}")
            print("\n✓ 测试成功！")
        else:
            print("\n✗ 测试失败")
    else:
        print("测试文件不存在")

if __name__ == "__main__":
    # 可以选择运行测试或GUI
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_invoice_recognition()
    else:
        window = InvoiceProcessWindow()
        window.run()
