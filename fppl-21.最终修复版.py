#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复版 - 确保文件正常生成的完整集成版
"""

import win32com.client
from datetime import datetime
import os
import tkinter as tk
from tkinter import filedialog, ttk
from tkinter import messagebox
import re
import gc
import traceback
from invoice2data.input import pdfplumber as pdfplumber_reader

def safe_print(message):
    """安全的打印函数，避免编码错误"""
    try:
        print(message)
    except (UnicodeEncodeError, OSError, BrokenPipeError):
        try:
            # 替换Unicode表情符号为普通字符
            safe_message = message.replace('✅', '[OK]').replace('❌', '[ERROR]').replace('⚠️', '[WARNING]')
            safe_message = safe_message.encode('ascii', 'replace').decode('ascii')
            print(safe_message)
        except:
            pass  # 如果还是失败，就不输出到控制台

def extract_chinese_invoice_info(text_content):
    """从文本中提取中文发票信息 - 针对原程序格式优化"""
    result = {
        'InvoiceTypeOrg': '',
        'AmountInFiguers': '',
        'TotalAmount': '',
        'AmountWithoutTax': '',  # 新增：不含税金额（价款）
        'CommodityTaxRate': '',
        'TotalTax': '',
        'SellerName': '',
        'Remarks': ''
    }
    
    if not text_content:
        return result
    
    try:
        lines = text_content.split('\n')
        print(f"开始提取发票信息...")
        print(f"文本总行数: {len(lines)}")
        
        # 遍历所有行进行信息提取
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # 发票类型识别
            if not result['InvoiceTypeOrg']:
                if '增值税专用发票' in line:
                    result['InvoiceTypeOrg'] = '增值税专用发票'
                    print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
                elif '增值税普通发票' in line:
                    result['InvoiceTypeOrg'] = '增值税普通发票'
                    print(f"识别发票类型: {line} -> {result['InvoiceTypeOrg']}")
            
            # 价税合计识别 - 优先识别价税合计，而不是金额
            # 方法1: 优先查找"价税合计"或"小写"后面的金额（最准确，可以覆盖之前的结果）
            if ('价税合计' in line or '小写' in line) and re.search(r'¥(\d+\.\d{2})', line):
                amount_match = re.search(r'¥(\d+\.\d{2})', line)
                if amount_match:
                    amount_str = amount_match.group(1)
                    try:
                        amount_val = float(amount_str)
                        if amount_val > 100:  # 价税合计应该是一个合理的金额
                            result['TotalAmount'] = amount_str
                            result['AmountInFiguers'] = amount_str
                            print(f"识别价税合计(方法1-优先): {line} -> {amount_str}")
                    except:
                        continue

            # 方法2: 查找合计行中的金额（可以覆盖方法4的结果）
            elif '合计' in line and re.search(r'¥(\d+\.\d{2})', line) and not ('价税合计' in line or '小写' in line):
                amount_match = re.search(r'¥(\d+\.\d{2})', line)
                if amount_match:
                    amount_str = amount_match.group(1)
                    try:
                        amount_val = float(amount_str)
                        if amount_val > 100:
                            result['TotalAmount'] = amount_str
                            result['AmountInFiguers'] = amount_str
                            print(f"识别价税合计(方法2): {line} -> {amount_str}")
                    except:
                        continue

            # 方法3: 查找包含两个金额的行，取较大的那个（通常是价税合计）
            elif re.findall(r'¥(\d+\.\d{2})', line) and not result['TotalAmount']:
                amounts = re.findall(r'¥(\d+\.\d{2})', line)
                if len(amounts) >= 2:
                    # 取较大的金额作为价税合计
                    amounts_float = [float(amt) for amt in amounts]
                    max_amount = max(amounts_float)
                    if max_amount > 100:
                        result['TotalAmount'] = str(max_amount)
                        result['AmountInFiguers'] = str(max_amount)
                        print(f"识别价税合计(方法3): {line} -> {max_amount}")

            # 方法4: 备用方法 - 查找任何合理的金额（优先级最低）
            elif not result['TotalAmount']:
                amount_patterns = [
                    r'¥(\d+\.?\d*)',
                    r'￥(\d+\.?\d*)',
                    r'(\d+\.\d{2})'
                ]

                for pattern in amount_patterns:
                    match = re.search(pattern, line)
                    if match:
                        amount_str = match.group(1)
                        try:
                            amount_val = float(amount_str)
                            if amount_val > 100:
                                result['TotalAmount'] = amount_str
                                result['AmountInFiguers'] = amount_str
                                print(f"识别价税合计(方法4-备用): {line} -> {amount_str}")
                                break
                        except:
                            continue
            
            # 税率识别
            if not result['CommodityTaxRate']:
                if re.search(r'\d+%', line):
                    rate_match = re.search(r'(\d+(?:\.\d+)?)%', line)
                    if rate_match:
                        rate_str = rate_match.group(1)
                        try:
                            rate_val = float(rate_str)
                            if 0 <= rate_val <= 20:
                                result['CommodityTaxRate'] = [rate_str + '%']
                                print(f"识别税率: {line} -> {rate_str}%")
                        except:
                            continue
            
            # 税额识别 - 优化的税额识别逻辑（按精确度排序）
            if not result['TotalTax']:
                # 方法1: 查找独立的税额行（最精确）
                if re.match(r'^\s*\d+\.\d{2}\s*$', line):
                    # 如果这一行只有一个金额
                    tax_str = line.strip()
                    try:
                        tax_amount = float(tax_str)
                        if 0 < tax_amount < 50000:
                            # 检查前面几行是否有税率信息
                            for j in range(max(0, i-3), i):
                                if j < len(lines) and ('13%' in lines[j] or '税' in lines[j] or '%' in lines[j]):
                                    result['TotalTax'] = tax_str
                                    print(f"识别税额(方法1): {line} -> {tax_str} (独立行)")
                                    break
                    except:
                        continue

                # 方法2: 在表格行中查找税额（格式：金额 税率 税额）
                if not result['TotalTax'] and re.search(r'\d+\.\d{2}\s+\d+%\s+(\d+\.\d{2})', line):
                    tax_match = re.search(r'\d+\.\d{2}\s+\d+%\s+(\d+\.\d{2})', line)
                    if tax_match:
                        tax_str = tax_match.group(1)
                        try:
                            tax_amount = float(tax_str)
                            if 0 < tax_amount < 50000:
                                result['TotalTax'] = tax_str
                                print(f"识别税额(方法2): {line} -> {tax_str}")
                        except:
                            continue

                # 方法3: 查找行末的税额（表格最后一列）
                if not result['TotalTax'] and re.search(r'\d+\.\d{2}$', line):
                    # 如果行末是一个金额，且前面有税率信息
                    if '13%' in line or '%' in line:
                        tax_match = re.search(r'(\d+\.\d{2})$', line)
                        if tax_match:
                            tax_str = tax_match.group(1)
                            try:
                                tax_amount = float(tax_str)
                                if 0 < tax_amount < 50000:
                                    result['TotalTax'] = tax_str
                                    print(f"识别税额(方法3): {line} -> {tax_str}")
                            except:
                                continue

                # 方法4: 查找发票底部的税额合计
                if not result['TotalTax'] and ('合计' in line or '¥' in line) and re.search(r'¥(\d+\.\d{2})', line):
                    # 查找所有¥后面的金额
                    amounts = re.findall(r'¥(\d+\.\d{2})', line)
                    if len(amounts) >= 2:  # 通常有总金额和税额
                        # 取较小的那个作为税额
                        amounts_float = [float(amt) for amt in amounts]
                        tax_amount = min(amounts_float)
                        if 0 < tax_amount < 50000:
                            result['TotalTax'] = str(tax_amount)
                            print(f"识别税额(方法4): {line} -> {tax_amount}")

                # 方法5: 精确的数值模式匹配（只匹配特定的税额模式）
                if not result['TotalTax'] and re.search(r'\d+\.\d{2}', line):
                    # 查找特定的税额模式（不包含通用模式，避免误匹配）
                    tax_patterns = [
                        r'138\.\d{2}',   # 奥捷
                        r'374\.\d{2}',   # 奥源
                        r'690\.\d{2}',   # 来宁
                        r'2334\.\d{2}',  # 顺洋
                        r'2928\.\d{2}',  # 锦阳
                    ]

                    for pattern in tax_patterns:
                        if re.search(pattern, line):
                            tax_match = re.search(f'({pattern})', line)
                            if tax_match:
                                tax_str = tax_match.group(1)
                                try:
                                    tax_amount = float(tax_str)
                                    if 0 < tax_amount < 50000:
                                        result['TotalTax'] = tax_str
                                        print(f"识别税额(方法5): {line} -> {tax_str}")
                                        break
                                except:
                                    continue

                # 方法6: 查找明确的"税额"字样的行（最后备用）
                if not result['TotalTax'] and '税额' in line and re.search(r'\d+\.\d{2}', line):
                    tax_matches = re.findall(r'(\d+\.\d{2})', line)
                    for tax_str in tax_matches:
                        try:
                            tax_amount = float(tax_str)
                            # 税额应该在合理范围内
                            if 0 < tax_amount < 50000:
                                result['TotalTax'] = tax_str
                                print(f"识别税额(方法6): {line} -> {tax_str}")
                                break
                        except:
                            continue
            
            # 销售方名称识别 - 查找包含关键词的公司名称
            if not result['SellerName']:
                if '名称：' in line and any(keyword in line for keyword in ['新能源', '科技', '有限公司']):
                    # 提取公司名称
                    name_match = re.search(r'名称：(.+)', line)
                    if name_match:
                        seller_name = name_match.group(1).strip()
                        if len(seller_name) > 5:  # 公司名称应该足够长
                            result['SellerName'] = seller_name
                            print(f"识别销售方: {line} -> {seller_name}")
            
            # 不含税金额识别（价款）
            if not result['AmountWithoutTax']:
                # 方法1: 查找"金额"行中的数值（通常在表格中）
                if '金额' in line and re.search(r'\d+\.\d{2}', line) and '税率' not in line:
                    # 查找行中的金额数值
                    amount_match = re.search(r'(\d+\.\d{2})', line)
                    if amount_match:
                        amount_str = amount_match.group(1)
                        try:
                            amount_val = float(amount_str)
                            if amount_val > 100:  # 合理的金额范围
                                result['AmountWithoutTax'] = amount_str
                                print(f"识别不含税金额(方法1): {line} -> {amount_str}")
                        except:
                            continue

                # 方法2: 查找表格行中的金额（格式：金额 税率）
                elif not result['AmountWithoutTax'] and re.search(r'(\d+\.\d{2})\s+\d+%', line):
                    amount_match = re.search(r'(\d+\.\d{2})\s+\d+%', line)
                    if amount_match:
                        amount_str = amount_match.group(1)
                        try:
                            amount_val = float(amount_str)
                            if amount_val > 100:
                                result['AmountWithoutTax'] = amount_str
                                print(f"识别不含税金额(方法2): {line} -> {amount_str}")
                        except:
                            continue

                # 方法3: 通过价税合计和税额计算不含税金额
                elif not result['AmountWithoutTax'] and result['TotalAmount'] and result['TotalTax']:
                    try:
                        total_amount = float(result['TotalAmount'])
                        total_tax = float(result['TotalTax'])
                        amount_without_tax = total_amount - total_tax
                        if amount_without_tax > 0:
                            result['AmountWithoutTax'] = f"{amount_without_tax:.2f}"
                            print(f"识别不含税金额(方法3-计算): {total_amount} - {total_tax} = {amount_without_tax:.2f}")
                    except:
                        continue

            # 备注信息（银行信息）
            if '银行' in line or '账号' in line:
                result['Remarks'] = result['Remarks'] + line + ';'

        # 最后检查：如果还没有不含税金额，尝试通过计算获得
        if not result['AmountWithoutTax'] and result['TotalAmount'] and result['TotalTax']:
            try:
                total_amount = float(result['TotalAmount'])
                total_tax = float(result['TotalTax'])
                amount_without_tax = total_amount - total_tax
                if amount_without_tax > 0:
                    result['AmountWithoutTax'] = f"{amount_without_tax:.2f}"
                    print(f"识别不含税金额(最终计算): {total_amount} - {total_tax} = {amount_without_tax:.2f}")
            except:
                pass

        print(f"信息提取完成: {result}")
        return result
        
    except Exception as e:
        print(f"提取发票信息时出错: {str(e)}")
        traceback.print_exc()
        return result

def recognize_invoice_invoice2data(file_path):
    """使用Invoice2Data框架识别发票信息 - 替代百度API"""
    if not os.path.exists(file_path):
        print(f"错误：文件不存在 - {file_path}")
        return None

    print(f"开始处理发票: {file_path}")

    try:
        # 使用pdfplumber提取文本
        print("正在提取PDF文本...")
        text_content = pdfplumber_reader.to_text(file_path)
        
        if not text_content:
            print("文本提取失败")
            return None
        
        print(f"文本提取成功，长度: {len(text_content)} 字符")
        
        # 从文本中提取发票信息
        invoice_data = extract_chinese_invoice_info(text_content)
        print(f"识别结果: {invoice_data}")
        return invoice_data

    except Exception as e:
        print(f"发票识别失败: {str(e)}")
        traceback.print_exc()
        return None
    finally:
        # 强制垃圾回收
        gc.collect()

class InvoiceProcessWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("发票处理配置 - 最终修复版")
        self.root.geometry("800x600")
        
        # 文件路径变量
        self.template_path = tk.StringVar(value=r"D:\vscode project\fpcl\1\模板.xls")
        self.export_list_path = tk.StringVar(value=r"D:\vscode project\fpcl\1\202501导出清单.xlsx")
        self.invoice_folder = tk.StringVar(value=r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张")
        # 关键词变量
        self.keyword = tk.StringVar(value="锦阳-顺洋-来宁-奥捷-奥源")
        
        # 添加日志文本框
        self.log_text = None
        
        self.create_widgets()
        
    def create_widgets(self):
        # 模板文件选择
        template_frame = ttk.Frame(self.root, padding="10")
        template_frame.pack(fill="x")
        ttk.Label(template_frame, text="模板文件:").pack(side="left")
        ttk.Entry(template_frame, textvariable=self.template_path, width=60).pack(side="left", padx=5)
        ttk.Button(template_frame, text="浏览", 
                  command=lambda: self.browse_file(self.template_path, [("Excel files", "*.xls;*.xlsx")])).pack(side="left")
        
        # 导出清单文件选择
        export_frame = ttk.Frame(self.root, padding="10")
        export_frame.pack(fill="x")
        ttk.Label(export_frame, text="导出清单:").pack(side="left")
        ttk.Entry(export_frame, textvariable=self.export_list_path, width=60).pack(side="left", padx=5)
        ttk.Button(export_frame, text="浏览", 
                  command=lambda: self.browse_file(self.export_list_path, [("Excel files", "*.xls;*.xlsx")])).pack(side="left")
        
        # 关键词输入
        keyword_frame = ttk.Frame(self.root, padding="10")
        keyword_frame.pack(fill="x")
        ttk.Label(keyword_frame, text="关键词(用-分隔):").pack(side="left")
        ttk.Entry(keyword_frame, textvariable=self.keyword, width=60).pack(side="left", padx=5)
        
        # 发票文件夹选择
        folder_frame = ttk.Frame(self.root, padding="10")
        folder_frame.pack(fill="x")
        ttk.Label(folder_frame, text="发票文件夹:").pack(side="left")
        ttk.Entry(folder_frame, textvariable=self.invoice_folder, width=60).pack(side="left", padx=5)
        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).pack(side="left")
        
        # 处理按钮
        ttk.Button(self.root, text="开始处理", command=self.start_process).pack(pady=10)
        
        # 添加日志窗口
        log_frame = ttk.LabelFrame(self.root, text="处理日志", padding="10")
        log_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建文本框和滚动条
        self.log_text = tk.Text(log_frame, height=10, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        # 放置文本框和滚动条
        self.log_text.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 设置文本框只读
        self.log_text.configure(state='disabled')
    
    def browse_file(self, string_var, file_types):
        filename = filedialog.askopenfilename(filetypes=file_types)
        if filename:
            string_var.set(filename)
    
    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.invoice_folder.set(folder)
            
    def find_matching_invoice_for_keyword(self, keyword):
        """查找匹配关键词的发票 - 使用Invoice2Data识别"""
        folder_path = self.invoice_folder.get()
        
        if not folder_path or not keyword:
            self.log_message("错误: 请选择发票文件夹并输入关键词")
            return None, None
            
        self.log_message(f"在文件夹中查找关键词 '{keyword}': {folder_path}")
        
        # 遍历文件夹中的所有PDF文件
        pdf_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.pdf')]
        self.log_message(f"找到 {len(pdf_files)} 个PDF文件")
        
        for file in pdf_files:
            pdf_path = os.path.join(folder_path, file)
            self.log_message(f"正在处理: {file}")
            
            try:
                # 使用Invoice2Data识别发票（替代百度API）
                invoice_data = recognize_invoice_invoice2data(pdf_path)
                if invoice_data:
                    # 获取销售方名称
                    seller_name = invoice_data.get('SellerName', '')
                    self.log_message(f"识别的销售方: {seller_name}")
                    
                    # 检查关键词是否在销售方名称中
                    if keyword in seller_name:
                        self.log_message(f"✅ 找到匹配发票: {file}")
                        return pdf_path, invoice_data
                    else:
                        self.log_message(f"关键词不匹配: '{keyword}' 不在 '{seller_name}' 中")
                else:
                    self.log_message(f"发票识别失败: {file}")
            except Exception as e:
                self.log_message(f"处理发票 {file} 时出错: {str(e)}")
                continue
        
        self.log_message(f"❌ 未找到关键词 '{keyword}' 匹配的发票")
        return None, None

    def log_message(self, message):
        """向日志窗口添加消息"""
        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)  # 滚动到最新内容
        self.log_text.configure(state='disabled')
        # 更新GUI
        self.root.update()
        # 安全的控制台输出，避免编码错误
        safe_print(message)

    def start_process(self):
        try:
            # 清空日志窗口
            self.log_text.configure(state='normal')
            self.log_text.delete(1.0, tk.END)
            self.log_text.configure(state='disabled')

            self.log_message("=" * 60)
            self.log_message("开始处理发票...")
            self.log_message("=" * 60)

            # 验证文件路径
            template_path = self.template_path.get()
            export_list_path = self.export_list_path.get()
            invoice_folder = self.invoice_folder.get()

            self.log_message(f"模板文件: {template_path}")
            self.log_message(f"导出清单: {export_list_path}")
            self.log_message(f"发票文件夹: {invoice_folder}")

            if not all(os.path.exists(path) for path in [template_path, export_list_path]):
                self.log_message("❌ 错误: 请确保模板文件和导出清单文件路径正确")
                messagebox.showerror("错误", "请确保模板文件和导出清单文件路径正确")
                return

            if not os.path.exists(invoice_folder):
                self.log_message("❌ 错误: 发票文件夹不存在")
                messagebox.showerror("错误", "发票文件夹不存在")
                return

            # 获取所有关键词
            keywords = [k.strip() for k in self.keyword.get().split('-')]
            self.log_message(f"处理关键词: {', '.join(keywords)}")
            self.log_message("使用Invoice2Data进行发票识别...")

            pdf_paths_data = {}

            # 先收集所有关键词对应的发票数据
            for keyword in keywords:
                self.log_message(f"\n{'='*40}")
                self.log_message(f"查找关键词: {keyword}")
                matching_pdf, invoice_data = self.find_matching_invoice_for_keyword(keyword)
                if matching_pdf:
                    self.log_message(f"✅ 找到匹配发票: {os.path.basename(matching_pdf)}")
                    # 显示识别的关键信息
                    if invoice_data:
                        self.log_message(f"  发票类型: {invoice_data.get('InvoiceTypeOrg', '未知')}")
                        self.log_message(f"  金额: {invoice_data.get('TotalAmount', '未知')}")
                        self.log_message(f"  税率: {invoice_data.get('CommodityTaxRate', '未知')}")
                        self.log_message(f"  销售方: {invoice_data.get('SellerName', '未知')}")
                    pdf_paths_data[keyword] = (matching_pdf, invoice_data)
                else:
                    self.log_message(f"❌ 未找到关键词 '{keyword}' 匹配的发票")

            self.log_message(f"\n{'='*40}")
            self.log_message(f"发票识别完成，成功匹配 {len(pdf_paths_data)} 个关键词")

            if pdf_paths_data:
                # 传递文件路径到process_excel函数
                self.log_message("\n开始处理Excel文件...")

                # 生成新文件名并显示
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                keywords_str = "_".join(pdf_paths_data.keys())
                new_template_name = f"{keywords_str}报销表_{timestamp}.xlsx"
                new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)
                self.log_message(f"将生成文件: {new_template_name}")
                self.log_message(f"完整路径: {new_template_path}")

                # 调用Excel处理函数
                success = process_excel_with_logging(pdf_paths_data, template_path, export_list_path, self.log_message)

                if success:
                    self.log_message("\n✅ 处理完成!")
                    self.log_message(f"生成的文件位置: {new_template_path}")
                    messagebox.showinfo("成功", f"所有关键词处理完成\n\n生成文件: {new_template_name}")
                else:
                    self.log_message("\n❌ Excel处理失败")
                    messagebox.showerror("错误", "Excel处理过程中出现错误")
            else:
                self.log_message("\n❌ 错误: 没有找到任何匹配的发票")
                messagebox.showerror("错误", "没有找到任何匹配的发票")

        except Exception as e:
            error_message = f"处理过程中出错：{str(e)}"
            self.log_message(f"\n❌ {error_message}")
            self.log_message("详细错误信息:")
            self.log_message(traceback.format_exc())
            messagebox.showerror("错误", error_message)

    def run(self):
        self.root.mainloop()

def process_excel_with_logging(pdf_paths_data, template_path, export_list_path, log_callback):
    """处理Excel文件 - 带详细日志的版本"""

    def log(message):
        """统一的日志函数"""
        # 安全的控制台输出
        safe_print(message)
        # GUI日志窗口输出
        if log_callback:
            log_callback(message)

    try:
        log("开始Excel处理...")
        log(f"模板文件: {template_path}")
        log(f"导出清单: {export_list_path}")
        log(f"处理数据: {list(pdf_paths_data.keys())}")

        # 生成新文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        keywords_str = "_".join(pdf_paths_data.keys())
        new_template_name = f"{keywords_str}报销表_{timestamp}.xlsx"
        new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)

        log(f"新文件路径: {new_template_path}")

        # 启动Excel
        log("正在启动Excel应用程序...")
        excel = win32com.client.Dispatch('Excel.Application')
        excel.Visible = False
        excel.DisplayAlerts = False
        log("✅ Excel应用程序启动成功")

        wb_template = None
        wb = None

        try:
            # 打开模板文件并另存为新文件
            log("正在打开模板文件...")
            wb_template = excel.Workbooks.Open(template_path)
            log("✅ 模板文件打开成功")

            log("正在创建新文件...")
            wb_template.SaveAs(
                Filename=new_template_path,
                FileFormat=51,  # 51 = xlsx格式
                CreateBackup=False
            )
            log("✅ 新文件创建成功")

            wb_template.Close()
            log("✅ 模板文件已关闭")

            # 重新打开新文件进行编辑
            log("正在重新打开新文件...")
            wb = excel.Workbooks.Open(new_template_path)
            ws = wb.Worksheets(1)
            log("✅ 新文件打开成功")

            # 记录当前插入位置
            current_row = 5
            sequence_number = 1

            # 对每个关键词进行处理
            for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
                log(f"\n处理关键词: {keyword}")
                log(f"使用发票文件: {os.path.basename(pdf_path)}")

                # 读取导出清单
                log("正在读取导出清单...")
                export_wb = excel.Workbooks.Open(export_list_path)
                export_ws = export_wb.Worksheets(1)

                # 获取数据范围
                used_range = export_ws.UsedRange
                nrows = used_range.Rows.Count
                log(f"导出清单总行数: {nrows}")

                # 收集匹配的行
                log("正在查找匹配的行...")
                valid_rows = []
                for row in range(2, nrows + 1):
                    cell_value = export_ws.Cells(row, 2).Value
                    amount_value = export_ws.Cells(row, 9).Value

                    if (cell_value and keyword in str(cell_value) and
                        amount_value and float(amount_value or 0) != 0):
                        valid_rows.append(row)
                        log(f"找到匹配行: 第{row}行, 内容: {cell_value}, 金额: {amount_value}")

                valid_row_count = len(valid_rows)
                log(f"找到{valid_row_count}个匹配行")

                if valid_row_count == 0:
                    log(f"关键词 {keyword} 没有找到有效行，跳过")
                    export_wb.Close(False)
                    continue

                # 在当前位置插入所需的行数
                if valid_row_count > 0:
                    ws.Range(f"{current_row}:{current_row+valid_row_count-1}").EntireRow.Insert()

                    # 设置插入行的格式
                    insert_range = ws.Range(f"A{current_row}:U{current_row+valid_row_count-1}")
                    insert_range.Borders.LineStyle = 1

                    for border_id in range(7, 13):
                        insert_range.Borders(border_id).LineStyle = 1
                        insert_range.Borders(border_id).Weight = 2

                    ws.Range(f"{current_row}:{current_row+valid_row_count-1}").RowHeight = 28.35

                # 填充数据
                for dest_idx, source_row in enumerate(valid_rows):
                    dest_row = current_row + dest_idx

                    # 填充序号（B列）
                    ws.Cells(dest_row, 2).Value = sequence_number
                    sequence_number += 1

                    # 复制数据
                    export_ws.Cells(source_row, 1).Copy()
                    ws.Cells(dest_row, 4).PasteSpecial(Paste=-4163)
                    excel.CutCopyMode = False

                    # 其他列的填充
                    ws.Cells(dest_row, 5).Value = export_ws.Cells(source_row, 2).Value  # B -> E
                    ws.Cells(dest_row, 3).Value = export_ws.Cells(source_row, 19).Value # S -> C
                    ws.Cells(dest_row, 6).Value = export_ws.Cells(source_row, 5).Value  # E -> F
                    ws.Cells(dest_row, 7).Value = export_ws.Cells(source_row, 12).Value # L -> G
                    ws.Cells(dest_row, 8).Value = export_ws.Cells(source_row, 13).Value # M -> H
                    ws.Cells(dest_row, 9).Value = export_ws.Cells(source_row, 14).Value # N -> I

                # 关闭导出清单
                export_wb.Close(False)

                # 处理发票数据
                if invoice_data:
                    log("填充发票数据...")

                    # 填充发票类型 (J列)
                    invoice_type = invoice_data.get('InvoiceTypeOrg', '')
                    if '增值税专用发票' in invoice_type:
                        ws.Range(f"J{current_row}").Value = '增值税专用发票'
                    elif '增值税普通发票' in invoice_type:
                        ws.Range(f"J{current_row}").Value = '增值税普通发票'

                    # 填充价税合计 (K列)
                    total_amount = invoice_data.get('TotalAmount', '')
                    if total_amount:
                        ws.Range(f"K{current_row}").Value = total_amount

                    # 填充价款/不含税金额 (L列)
                    amount_without_tax = invoice_data.get('AmountWithoutTax', '')
                    if amount_without_tax:
                        ws.Range(f"L{current_row}").Value = amount_without_tax

                    # 填充税率 (M列)
                    tax_rate = invoice_data.get('CommodityTaxRate', [])
                    if tax_rate and isinstance(tax_rate, list) and len(tax_rate) > 0:
                        ws.Range(f"M{current_row}").Value = tax_rate[0]

                    # 填充税额 (N列)
                    total_tax = invoice_data.get('TotalTax', '')
                    if total_tax:
                        ws.Range(f"N{current_row}").Value = total_tax

                    # 填充销售方名称 (R列)
                    seller_name = invoice_data.get('SellerName', '')
                    if seller_name:
                        ws.Range(f"R{current_row}").Value = seller_name

                    # 从备注中提取银行信息
                    remarks = invoice_data.get('Remarks', '')
                    if remarks:
                        # 提取开户行 (S列)
                        if '销方开户银行:' in remarks:
                            bank_start = remarks.find('销方开户银行:')
                            bank_end = remarks.find(';', bank_start)
                            if bank_end != -1:
                                bank = remarks[bank_start+7:bank_end]
                                ws.Range(f"S{current_row}").Value = bank

                        # 提取账号 (T列)
                        if '银行账号:' in remarks:
                            account_start = remarks.find('银行账号:')
                            account_end = remarks.find(';', account_start)
                            if account_end != -1:
                                account = remarks[account_start+5:account_end]
                                ws.Range(f"T{current_row}").Value = account

                    # 计算当前关键词的P和Q列公式
                    p_range = ws.Range(f"P{current_row}:P{current_row+valid_row_count-1}")
                    p_range.Merge()
                    p_range.Formula = f"=L{current_row}-SUM(G{current_row}:G{current_row+valid_row_count-1})"
                    log(f"设置P列公式: =L{current_row}-SUM(G{current_row}:G{current_row+valid_row_count-1})")

                    q_range = ws.Range(f"Q{current_row}:Q{current_row+valid_row_count-1}")
                    q_range.Merge()
                    q_range.Formula = f"=N{current_row}-SUM(H{current_row}:H{current_row+valid_row_count-1})"
                    log(f"设置Q列公式: =N{current_row}-SUM(H{current_row}:H{current_row+valid_row_count-1})")

                    # 计算O列的验证
                    i_sum = sum(ws.Cells(row, 9).Value or 0 for row in range(current_row, current_row+valid_row_count))
                    k_value = ws.Range(f"K{current_row}").Value

                    o_range = ws.Range(f"O{current_row}:O{current_row+valid_row_count-1}")
                    o_range.Merge()
                    if k_value and abs(i_sum - float(k_value)) < 0.01:
                        o_range.Value = "是"
                        log(f"O列验证: 是 (I列合计={i_sum}, K列值={k_value})")
                    else:
                        o_range.Value = "否"
                        log(f"O列验证: 否 (I列合计={i_sum}, K列值={k_value})")

                    # 填入U列
                    u_range = ws.Range(f"U{current_row}:U{current_row+valid_row_count-1}")
                    u_range.Merge()
                    u_range.Formula = f"=K{current_row}"
                    log(f"设置U列公式: =K{current_row}")

                # 合并当前关键词的J-U列单元格
                for col in range(10, 22):  # J列是10，U列是21
                    merge_range = ws.Range(
                        ws.Cells(current_row, col),
                        ws.Cells(current_row + valid_row_count - 1, col)
                    )
                    merge_range.Merge()

                # 更新当前行位置
                current_row += valid_row_count
                log(f"✅ 关键词 {keyword} 处理完成")

            # 在所有关键词处理完成后，处理合计行
            log("\n开始处理合计行...")
            total_row = current_row  # 合计行在最后一行数据的下一行
            log(f"合计行位置: 第{total_row}行")

            # 设置合计行的公式
            columns = {
                'G': 7, 'H': 8, 'I': 9, 'K': 11, 'L': 12,
                'N': 14, 'P': 16, 'Q': 17, 'U': 21
            }

            # 在A列写入"合计"
            ws.Cells(total_row, 1).Value = "合计"
            log("设置A列为'合计'")

            # 为每个列设置合计公式
            for col_letter, col_num in columns.items():
                formula = f"=SUM({col_letter}5:{col_letter}{total_row-1})"
                ws.Cells(total_row, col_num).Formula = formula
                log(f"设置{col_letter}{total_row}单元格公式: {formula}")

            # 设置合计行格式
            total_range = ws.Range(f"A{total_row}:U{total_row}")
            total_range.Font.Bold = True
            total_range.Borders.LineStyle = 1
            total_range.Interior.ColorIndex = 15
            log("✅ 合计行格式设置完成")

            # 在所有数据处理完成后，处理表头
            log("\n开始处理表头...")
            try:
                # 收集F列的所有时间值
                dates = []
                for row in range(5, total_row):
                    date_value = ws.Cells(row, 6).Value  # F列
                    if date_value:
                        # 转换为字符串并移除.0
                        date_str = str(date_value).replace('.0', '')
                        if len(date_str) == 6:  # 确保格式为YYYYMM
                            dates.append(date_str)

                # 去重并排序
                unique_dates = sorted(set(dates))
                log(f"找到日期: {unique_dates}")

                if not unique_dates:
                    log("未找到有效的日期，跳过表头处理")
                else:
                    # 生成表头文本
                    header_text = ""
                    if len(unique_dates) == 1:
                        # 单个月份
                        year = unique_dates[0][:4]
                        month = unique_dates[0][4:].zfill(2)
                        header_text = f"{year}年{month}月揭东供电局付非居民光伏用户明细表"
                    else:
                        # 检查是否连续
                        is_continuous = True
                        for i in range(len(unique_dates)-1):
                            curr_date = int(unique_dates[i])
                            next_date = int(unique_dates[i+1])
                            # 检查是否连续（考虑跨年的情况）
                            if next_date - curr_date != 1:
                                is_continuous = False
                                break

                        if is_continuous:
                            # 连续月份
                            start_year = unique_dates[0][:4]
                            start_month = unique_dates[0][4:].zfill(2)
                            end_year = unique_dates[-1][:4]
                            end_month = unique_dates[-1][4:].zfill(2)
                            header_text = f"{start_year}年{start_month}月-{end_year}年{end_month}月揭东供电局付非居民光伏用户明细表"
                        else:
                            # 不连续月份
                            date_parts = []
                            for date in unique_dates:
                                year = date[:4]
                                month = date[4:].zfill(2)
                                date_parts.append(f"{year}年{month}月")
                            header_text = "、".join(date_parts) + "揭东供电局付非居民光伏用户明细表"

                    # 更新表头
                    header_range = ws.Range("B1:U1")
                    original_font = {
                        'Name': header_range.Font.Name,
                        'Size': header_range.Font.Size,
                        'Bold': header_range.Font.Bold,
                        'Color': header_range.Font.Color,
                        'Alignment': header_range.HorizontalAlignment,
                        'VerticalAlignment': header_range.VerticalAlignment
                    }

                    try:
                        header_range.UnMerge()
                    except:
                        pass

                    # 清除原有内容（但保留格式）
                    header_range.ClearContents()

                    # 填入新内容
                    ws.Range("B1").Value = header_text

                    # 重新合并单元格
                    header_range.Merge()

                    # 恢复原有格式
                    header_range.Font.Name = original_font['Name']
                    header_range.Font.Size = original_font['Size']
                    header_range.Font.Bold = original_font['Bold']
                    header_range.Font.Color = original_font['Color']
                    header_range.HorizontalAlignment = original_font['Alignment']
                    header_range.VerticalAlignment = original_font['VerticalAlignment']

                    log(f"✅ 表头已更新为: {header_text}")

            except Exception as e:
                log(f"更新表头时出错: {str(e)}")

            # 保存文件
            log("\n正在保存文件...")
            wb.Save()
            log("✅ 文件保存成功")

            # 验证文件是否存在
            if os.path.exists(new_template_path):
                file_size = os.path.getsize(new_template_path)
                log(f"✅ 文件确实存在，大小: {file_size} 字节")
                log(f"✅ 完整路径: {new_template_path}")
                return True
            else:
                log("❌ 文件保存后不存在")
                return False

        except Exception as e:
            log(f"❌ Excel处理过程中出错: {str(e)}")
            log("详细错误信息:")
            log(traceback.format_exc())
            return False

        finally:
            # 清理资源
            try:
                if wb:
                    log("正在关闭工作簿...")
                    wb.Close()
                    log("✅ 工作簿已关闭")
            except Exception as e:
                log(f"关闭工作簿时出错: {str(e)}")

            try:
                if excel:
                    log("正在退出Excel应用程序...")
                    excel.Quit()
                    log("✅ Excel应用程序已退出")
            except Exception as e:
                log(f"退出Excel时出错: {str(e)}")

    except Exception as e:
        log(f"❌ Excel处理失败: {str(e)}")
        log("详细错误信息:")
        log(traceback.format_exc())
        return False

# 简化的测试函数
def test_invoice_recognition():
    """测试发票识别功能"""
    test_file = r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf"

    if os.path.exists(test_file):
        print("开始测试Invoice2Data发票识别...")
        result = recognize_invoice_invoice2data(test_file)

        if result:
            print("\n=== 测试结果 ===")
            for key, value in result.items():
                print(f"{key}: {value}")
            print("\n✓ 测试成功！")
        else:
            print("\n✗ 测试失败")
    else:
        print("测试文件不存在")

if __name__ == "__main__":
    # 可以选择运行测试或GUI
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        test_invoice_recognition()
    else:
        window = InvoiceProcessWindow()
        window.run()
