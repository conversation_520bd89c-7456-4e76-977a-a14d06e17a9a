info = {
    "name": "nus",
    "date_order": "DMY",
    "january": [
        "tiop",
        "tiop thar pɛt"
    ],
    "february": [
        "pɛt"
    ],
    "march": [
        "duɔ̱ɔ̱",
        "duɔ̱ɔ̱ŋ"
    ],
    "april": [
        "guak"
    ],
    "may": [
        "duä",
        "duät"
    ],
    "june": [
        "kor",
        "kornyoot"
    ],
    "july": [
        "pay",
        "pay yie̱tni"
    ],
    "august": [
        "thoo",
        "tho̱o̱r"
    ],
    "september": [
        "tɛɛ",
        "tɛɛr"
    ],
    "october": [
        "laa",
        "laath"
    ],
    "november": [
        "kur"
    ],
    "december": [
        "tid",
        "tio̱p in di̱i̱t"
    ],
    "monday": [
        "jiec",
        "jiec la̱t"
    ],
    "tuesday": [
        "rɛw",
        "rɛw lätni"
    ],
    "wednesday": [
        "diɔ̱k",
        "diɔ̱k lätni"
    ],
    "thursday": [
        "ŋuaan",
        "ŋuaan lätni"
    ],
    "friday": [
        "dhieec",
        "dhieec lät<PERSON>"
    ],
    "saturday": [
        "bäkɛl",
        "bäkɛl lätni"
    ],
    "sunday": [
        "cäŋ",
        "cäŋ kuɔth"
    ],
    "am": [
        "rw"
    ],
    "pm": [
        "tŋ"
    ],
    "year": [
        "ruɔ̱n"
    ],
    "month": [
        "pay"
    ],
    "week": [
        "jiɔk"
    ],
    "day": [
        "cäŋ"
    ],
    "hour": [
        "thaak"
    ],
    "minute": [
        "minit"
    ],
    "second": [
        "thɛkɛni"
    ],
    "relative-type": {
        "0 day ago": [
            "walɛ"
        ],
        "0 hour ago": [
            "this hour"
        ],
        "0 minute ago": [
            "this minute"
        ],
        "0 month ago": [
            "this month"
        ],
        "0 second ago": [
            "now"
        ],
        "0 week ago": [
            "this week"
        ],
        "0 year ago": [
            "this year"
        ],
        "1 day ago": [
            "pan"
        ],
        "1 month ago": [
            "last month"
        ],
        "1 week ago": [
            "last week"
        ],
        "1 year ago": [
            "last year"
        ],
        "in 1 day": [
            "ruun"
        ],
        "in 1 month": [
            "next month"
        ],
        "in 1 week": [
            "next week"
        ],
        "in 1 year": [
            "next year"
        ]
    },
    "locale_specific": {},
    "skip": [
        " ",
        "'",
        ",",
        "-",
        ".",
        "/",
        ";",
        "@",
        "[",
        "]",
        "|",
        "，"
    ]
}
