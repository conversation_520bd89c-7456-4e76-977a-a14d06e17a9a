#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试N、O、P、Q列和合计行的处理
"""

import win32com.client
from datetime import datetime
import os
import traceback

def test_nopq_columns_processing():
    """测试N、O、P、Q列的处理"""
    
    # 模拟真实的发票数据
    pdf_paths_data = {
        "奥捷": (
            r"D:/vscode project/发票处理/1/202501顺洋、来宁、锦阳、奥捷、奥源-5张/广东奥捷_25442000000085679219_广东电网有限责任公司揭阳供电局_20250217173457.pdf",
            {
                'InvoiceTypeOrg': '增值税专用发票',
                'AmountInFiguers': '1065.15',
                'TotalAmount': '1065.15',
                'CommodityTaxRate': ['13%'],
                'TotalTax': '138.47',
                'SellerName': '广东奥捷新能源科技有限公司',
                'Remarks': '购方开户银行:中国工商银行揭阳分行;银行账号:2019002129200505667;销方开户银行:建设银行爵溪支行;银行账号:33150199555600000291;'
            }
        )
    }
    
    template_path = r"D:\vscode project\fpcl\1\模板.xls"
    export_list_path = r"D:\vscode project\fpcl\1\202501导出清单.xlsx"
    
    print("开始测试N、O、P、Q列处理...")
    print(f"模板文件: {template_path}")
    print(f"导出清单: {export_list_path}")
    
    # 检查文件是否存在
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    if not os.path.exists(export_list_path):
        print(f"❌ 导出清单不存在: {export_list_path}")
        return
    
    print("✅ 文件存在检查通过")
    
    # 生成新文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    keywords_str = "_".join(pdf_paths_data.keys())
    new_template_name = f"测试NOPQ列_{keywords_str}_{timestamp}.xlsx"
    new_template_path = os.path.join(os.path.dirname(template_path), new_template_name)
    
    print(f"新文件路径: {new_template_path}")
    
    excel = None
    wb_template = None
    wb = None
    
    try:
        print("正在启动Excel应用程序...")
        excel = win32com.client.Dispatch('Excel.Application')
        excel.Visible = False
        excel.DisplayAlerts = False
        print("✅ Excel应用程序启动成功")
        
        print("正在打开模板文件...")
        wb_template = excel.Workbooks.Open(template_path)
        print("✅ 模板文件打开成功")
        
        print("正在另存为新文件...")
        wb_template.SaveAs(
            Filename=new_template_path,
            FileFormat=51,  # 51 = xlsx格式
            CreateBackup=False
        )
        print("✅ 新文件创建成功")
        
        wb_template.Close()
        print("✅ 模板文件已关闭")
        
        print("正在重新打开新文件...")
        wb = excel.Workbooks.Open(new_template_path)
        ws = wb.Worksheets(1)
        print("✅ 新文件打开成功")
        
        # 模拟数据填充
        current_row = 5
        
        for keyword, (pdf_path, invoice_data) in pdf_paths_data.items():
            print(f"\n处理关键词: {keyword}")
            
            # 读取导出清单
            print("正在读取导出清单...")
            export_wb = excel.Workbooks.Open(export_list_path)
            export_ws = export_wb.Worksheets(1)
            
            # 获取数据范围
            used_range = export_ws.UsedRange
            nrows = used_range.Rows.Count
            print(f"导出清单总行数: {nrows}")
            
            # 查找匹配的行
            valid_rows = []
            for row in range(2, nrows + 1):
                cell_value = export_ws.Cells(row, 2).Value
                amount_value = export_ws.Cells(row, 9).Value
                
                if (cell_value and keyword in str(cell_value) and
                    amount_value and float(amount_value or 0) != 0):
                    valid_rows.append(row)
                    print(f"找到匹配行: 第{row}行, 内容: {cell_value}, 金额: {amount_value}")
            
            valid_row_count = len(valid_rows)
            print(f"找到{valid_row_count}个匹配行")
            
            if valid_row_count > 0:
                # 插入行
                ws.Range(f"{current_row}:{current_row+valid_row_count-1}").EntireRow.Insert()
                
                # 填充基础数据
                for i, source_row in enumerate(valid_rows):
                    dest_row = current_row + i
                    ws.Cells(dest_row, 2).Value = i + 1  # 序号
                    ws.Cells(dest_row, 5).Value = export_ws.Cells(source_row, 2).Value  # B -> E
                    ws.Cells(dest_row, 7).Value = export_ws.Cells(source_row, 12).Value # L -> G
                    ws.Cells(dest_row, 8).Value = export_ws.Cells(source_row, 13).Value # M -> H
                    ws.Cells(dest_row, 9).Value = export_ws.Cells(source_row, 14).Value # N -> I
                
                # 填充发票数据
                if invoice_data:
                    print("填充发票数据...")
                    
                    # J列 - 发票类型
                    ws.Cells(current_row, 10).Value = invoice_data.get('InvoiceTypeOrg', '')
                    print(f"J列 (发票类型): {invoice_data.get('InvoiceTypeOrg', '')}")
                    
                    # K列 - 金额
                    amount = invoice_data.get('AmountInFiguers', '')
                    ws.Cells(current_row, 11).Value = amount
                    print(f"K列 (金额): {amount}")
                    
                    # L列 - 总金额
                    total_amount = invoice_data.get('TotalAmount', '')
                    ws.Cells(current_row, 12).Value = total_amount
                    print(f"L列 (总金额): {total_amount}")
                    
                    # M列 - 税率
                    tax_rate = invoice_data.get('CommodityTaxRate', [])
                    if tax_rate and len(tax_rate) > 0:
                        ws.Cells(current_row, 13).Value = tax_rate[0]
                        print(f"M列 (税率): {tax_rate[0]}")
                    
                    # N列 - 税额
                    total_tax = invoice_data.get('TotalTax', '')
                    ws.Cells(current_row, 14).Value = total_tax
                    print(f"N列 (税额): {total_tax}")
                    
                    # 重点测试：O、P、Q列的计算
                    print("\n=== 重点测试：O、P、Q列计算 ===")
                    
                    # P列公式：L列 - G列合计
                    p_range = ws.Range(f"P{current_row}:P{current_row+valid_row_count-1}")
                    p_range.Merge()
                    p_formula = f"=L{current_row}-SUM(G{current_row}:G{current_row+valid_row_count-1})"
                    p_range.Formula = p_formula
                    print(f"P列公式: {p_formula}")
                    
                    # Q列公式：N列 - H列合计
                    q_range = ws.Range(f"Q{current_row}:Q{current_row+valid_row_count-1}")
                    q_range.Merge()
                    q_formula = f"=N{current_row}-SUM(H{current_row}:H{current_row+valid_row_count-1})"
                    q_range.Formula = q_formula
                    print(f"Q列公式: {q_formula}")
                    
                    # O列验证：检查I列合计是否等于K列
                    i_sum = sum(ws.Cells(row, 9).Value or 0 for row in range(current_row, current_row+valid_row_count))
                    k_value = ws.Cells(current_row, 11).Value
                    
                    o_range = ws.Range(f"O{current_row}:O{current_row+valid_row_count-1}")
                    o_range.Merge()
                    if k_value and abs(i_sum - float(k_value)) < 0.01:
                        o_range.Value = "是"
                        print(f"O列验证: 是 (I列合计={i_sum}, K列值={k_value})")
                    else:
                        o_range.Value = "否"
                        print(f"O列验证: 否 (I列合计={i_sum}, K列值={k_value})")
                    
                    # U列公式
                    u_range = ws.Range(f"U{current_row}:U{current_row+valid_row_count-1}")
                    u_range.Merge()
                    u_formula = f"=K{current_row}"
                    u_range.Formula = u_formula
                    print(f"U列公式: {u_formula}")
                
                current_row += valid_row_count
            
            export_wb.Close(False)
            print(f"✅ 关键词 {keyword} 处理完成")
        
        # 测试合计行
        print(f"\n=== 重点测试：合计行处理 ===")
        total_row = current_row
        print(f"合计行位置: 第{total_row}行")
        
        # 设置合计行
        columns = {
            'G': 7, 'H': 8, 'I': 9, 'K': 11, 'L': 12,
            'N': 14, 'P': 16, 'Q': 17, 'U': 21
        }
        
        # 在A列写入"合计"
        ws.Cells(total_row, 1).Value = "合计"
        print("设置A列为'合计'")
        
        # 为每个列设置合计公式
        for col_letter, col_num in columns.items():
            formula = f"=SUM({col_letter}5:{col_letter}{total_row-1})"
            ws.Cells(total_row, col_num).Formula = formula
            print(f"设置{col_letter}{total_row}单元格公式: {formula}")
        
        # 设置合计行格式
        total_range = ws.Range(f"A{total_row}:U{total_row}")
        total_range.Font.Bold = True
        total_range.Borders.LineStyle = 1
        total_range.Interior.ColorIndex = 15
        print("✅ 合计行格式设置完成")
        
        print("\n正在保存文件...")
        wb.Save()
        print("✅ 文件保存成功")
        
        # 验证文件
        if os.path.exists(new_template_path):
            file_size = os.path.getsize(new_template_path)
            print(f"✅ 文件确实存在，大小: {file_size} 字节")
            print(f"✅ 完整路径: {new_template_path}")
            
            # 读取并验证关键单元格的值
            print(f"\n=== 验证关键单元格 ===")
            try:
                # 重新打开文件验证
                wb_verify = excel.Workbooks.Open(new_template_path)
                ws_verify = wb_verify.Worksheets(1)
                
                # 检查O、P、Q列的值
                o_value = ws_verify.Cells(5, 15).Value  # O5
                p_value = ws_verify.Cells(5, 16).Value  # P5
                q_value = ws_verify.Cells(5, 17).Value  # Q5
                u_value = ws_verify.Cells(5, 21).Value  # U5
                
                print(f"O5单元格值: {o_value}")
                print(f"P5单元格值: {p_value}")
                print(f"Q5单元格值: {q_value}")
                print(f"U5单元格值: {u_value}")
                
                # 检查合计行
                total_g = ws_verify.Cells(total_row, 7).Value   # G合计
                total_k = ws_verify.Cells(total_row, 11).Value  # K合计
                total_p = ws_verify.Cells(total_row, 16).Value  # P合计
                
                print(f"G列合计: {total_g}")
                print(f"K列合计: {total_k}")
                print(f"P列合计: {total_p}")
                
                wb_verify.Close()
                print("✅ 验证完成")
                
            except Exception as e:
                print(f"验证时出错: {str(e)}")
        else:
            print("❌ 文件保存后不存在")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        traceback.print_exc()
        
    finally:
        try:
            if wb:
                wb.Close()
            if excel:
                excel.Quit()
        except:
            pass

if __name__ == "__main__":
    print("N、O、P、Q列和合计行测试")
    print("=" * 50)
    test_nopq_columns_processing()
